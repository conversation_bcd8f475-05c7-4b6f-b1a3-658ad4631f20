name: incenti_ai
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+2

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get: ^4.6.6
  get_storage: ^2.1.1
  connectivity_plus: ^6.0.3
  dio: ^5.4.3+1
  intl: ^0.19.0
  flutter_html: any
  google_fonts: ^6.2.1
  url_launcher: ^6.3.0
  flutter_local_notifications: ^19.0.0
  firebase_messaging: ^15.1.2
  socket_io_client: ^3.1.2
  translator: ^1.0.0
  path_provider: ^2.1.4
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.2
  image_picker: ^1.1.2
  image_cropper: ^8.1.0
  kiwi: ^5.0.0
  flutter_svg: any
  lottie: ^3.1.2
  flutter_localization: ^0.2.1
  dotted_border: ^2.1.0
  signin_with_linkedin: ^1.0.1
  firebase_core: ^3.10.1
  firebase_auth: ^5.2.0
  flutter_colorpicker: ^1.1.0
  pretty_dio_logger: ^1.4.0
  shimmer: ^2.0.0
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.3
  pinput: ^5.0.1
  file_picker: any
  flutter_widget_from_html: any
  package_info_plus: ^8.0.0
  flutter_quill_delta_from_html: any
  readmore: any
  flutter_slidable: ^4.0.0
  flutter_quill:
    path: flutter-quill-master
  align_positioned: ^5.0.1
  video_player: ^2.9.3
#  ffmpeg_kit_flutter: ^6.0.3
  ffmpeg_kit_flutter_new: ^2.0.0
  flutter_background_service: ^5.1.0
  rxdart: ^0.28.0
  crop_your_image: ^2.0.0
  better_player_plus: ^1.0.8
  cached_network_image: ^3.4.1
  share_plus: ^10.1.4
  app_links: ^6.4.0
  vsc_quill_delta_to_html: ^1.0.5
  restart_app: ^1.3.2
  awesome_notifications: ^0.10.0
  flutter_image_compress: ^2.4.0
  youtube_player_flutter: ^9.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter

dependency_overrides:
  flutter_quill_extensions:
    path: flutter-quill-master/flutter_quill_extensions

  csslib: ^1.0.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  assets:
    - assets/images/
    - assets/svg/
    - assets/create/
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
