import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../../../constants/api.dart';
import '../../../../main.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/current_user.dart';
import '../../../routes/app_pages.dart';
import '../../story_editor/controllers/story_editor_controller.dart';

bool isConnected = true;
String lastRoute = "";
var args = Get.arguments;

class SplashController extends GetxController
    with SingleGetTickerProviderMixin {
  RxInt count = 0.obs;
  ApiManager apiManager = ApiManager();
  bool isFirstTime = true;
  RxBool hasData = false.obs;
  bool? isVersionMatched;
  late AnimationController animationController;
  late Animation<Color?> colorAnimation;
  late Animation<Offset> positionAnimation;
  late Animation<double> gradientAnimation;

  @override
  void onInit() async {
    super.onInit();
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3), // Duration of the animation
    );
    gradientAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: animationController, curve: Curves.linear),
    );
    // Add a listener to detect when the animation is complete
    // animationController.addStatusListener((status) async {
    //   if (status == AnimationStatus.completed) {
    //     callApiForAppConfig(context: Get.context!);
    //     // if(box.hasData('token')){
    //     //   CurrentUser.getMe(callback: () async {
    //     //     Get.offAllNamed(Routes.Bottom_Bar); // Replace with your desired route
    //     //   });
    //     // } else {
    //     //   Get.offAllNamed(Routes.ONBOARDING); // Replace with your desired route
    //     // }
    //   }
    // });
    animationController.forward();
    callApiForAppConfig(context: Get.context!);
    await StoryBackgroundUploadService.initialize();
    await Future.delayed(const Duration(seconds: 1), () async {
      Connectivity()
          .onConnectivityChanged
          .listen((List<ConnectivityResult> result) {
        if (isFirstTime && !Platform.isAndroid) {
          isFirstTime = false;
        } else {
          if (result[0] == ConnectivityResult.none) {
            isConnected = false;
            if (Get.currentRoute != Routes.NO_INTERNET) {
              lastRoute = Get.currentRoute;
              args = Get.arguments;
              Get.toNamed(Routes.NO_INTERNET)?.then((value) {
                if (Get.currentRoute == Routes.SPLASH) {
                  restartAnimation(); // Restart animation when coming back to splash
                }
              },);
            }
          } else {
            isConnected = true;

            if (Get.currentRoute == Routes.NO_INTERNET) {
              Get.offNamedUntil(
                  lastRoute,
                  (route) => !(route.settings.name == Routes.NO_INTERNET ||
                      route.settings.name == lastRoute),
                  arguments: args);

              if (Get.currentRoute == Routes.SPLASH) {
                // Get.delete<SplashController>();
                // Get.offAllNamed(Routes.SPLASH);
                restartAnimation();
              }
            }
          }
        }
      });

      // if (Platform.isAndroid) {
        if ((await Connectivity().checkConnectivity())[0] ==
            ConnectivityResult.none) {
          isConnected = false;
          Get.toNamed(Routes.NO_INTERNET)?.then((value) {
            restartAnimation();
          },);
        }
      // }
    });
  }
  void restartAnimation() {
    animationController.reverse();
    animationController.forward();
  }

  // @override
  // void onClose() {
  //   animationController.dispose();
  //   super.onClose();
  // }

  callApiForAppConfig({required BuildContext context}) async {
    FocusScope.of(context).unfocus();
    final PackageInfo info = await PackageInfo.fromPlatform();
    String appVersion = info.version;
    return apiManager.callApi(APIS.appConfig.get,
        successCallback: (response, message) async {
      Map<String, dynamic> configData = response['appConfig'];

      bool underMaintenance = configData['appInMaintenance'] == true;
      bool forceUpdate = configData['forceUpdate'] == true;
      bool softUpdate = configData['softUpdate'] == true;
      String androidVersion = configData['androidVersionCode'] ??
          "2.0.0"; // Default version if null
      String iosVersion =
          configData['iosVersionCode'] ?? "2.0.0"; // Default version if null

      if (underMaintenance) {
        Get.offAllNamed(Routes.APP_UNDER_MAINTENANCE);
      } else if (forceUpdate &&
          (Platform.isAndroid
              ? appVersion != androidVersion
              : appVersion != iosVersion)) {
        Get.offAllNamed(Routes.APP_UPDATE_AVAILABLE);
      } else if (softUpdate &&
          (Platform.isAndroid
              ? appVersion != androidVersion
              : appVersion != iosVersion)) {
        Get.offAllNamed(Routes.APP_UPDATE_AVAILABLE,
            arguments: {"softUpdate": true});
      } else {
        if (box.hasData('finalToken')) {
          CurrentUser.getMe(callback: () async {
            if(CurrentUser.user.customSetting?.homeSection == "profile") {
              Get.offAllNamed(
                  Routes.Bottom_Bar);
             await Future.delayed(Duration(milliseconds: 50),() {
              Get.toNamed(Routes.profile);
              },);
            } else {
              Get.offAllNamed(
                  Routes.Bottom_Bar);
            }

          });
        } else {
          Get.offNamed(Routes.ONBOARDING);
        }
      }

      log("::::::::::::::::::::APP CONFIG $response");
    }, failureCallback: (status, message) {
      // hasData.value = true;
    });
  }
}
