import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/project_detail_view/controllers/project_detail_view_controller.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../models/app_project_model.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/common_validation.dart';
import '../../../../utillites/delete_confirmation_dialog.dart';
import '../../../../utillites/typography.dart';

Widget createOrEditFolderBottomSheet(BuildContext context,
    {bool isEdit = false, ProjectFolder? folder,required ProjectDetailViewController controller}) {
  return BackdropFilter(
    filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
    child: Container(
      decoration: BoxDecoration(
        color: AppTheme.appBottomSheet,
        border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(40),
            topRight: Radius.circular(40)),
      ),
      child: Padding(
        padding:
        EdgeInsets.only(bottom: MediaQuery
            .of(context)
            .viewInsets
            .bottom),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Space.height(8),
            Container(
              height: MySize.size5,
              width: MySize.size34,
              decoration: BoxDecoration(
                color: box.read('isDarkMode') ? AppTheme.grey : AppTheme.black.withOpacity(0.2),
                borderRadius: BorderRadius.circular(100),
              ),
            ),
            Space.height(30),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
              child: Align(
                alignment: Alignment.centerRight,
                child: InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () {
                    Get.back();
                  },
                  child: SvgPicture.asset(
                    AppImage.closeImage,
                    height: MySize.size16,
                    width: MySize.size16,
                    color: AppTheme.whiteWithNull,
                  ),
                ),
              ),
            ),
            Space.height(10),
            Align(
              alignment: Alignment.center,
              child: TypoGraphy(
                text: 'New Folder',
                level: 8,
                fontWeight: FontWeight.w700,
              ),
            ),
            Space.height(10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
              child: StatefulBuilder(builder: (context, setTextState) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Space.height(20),
                    AppTextField(
                      controller: controller.folderNameController,
                      labelText: "Folder Name",
                      maxLength: 30,
                      textCapitalization: TextCapitalization.words,
                      onChangedValue: (p0) {
                        setTextState(() {});
                      },
                      validators: [
                            (value) =>
                            CommonValidation.isEmpty(value: value ?? ''),
                      ],
                    ),
                    Space.height(30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Buttons(
                            isDisable: controller.folderNameController.text
                                .trim()
                                .isEmpty,
                            disableColor:
                            AppTheme.primary1.withValues(alpha: 0.5),
                            textDisableColor: AppTheme.white,
                            width: MySize.size198,
                            buttonText: isEdit ? "Update" : "Create",
                            buttonTextLevel: 4,
                            onTap: () {
                              if (controller.folderNameController.value.text
                                  .trim()
                                  .isNotEmpty) {
                                if (isEdit) {
                                  controller.callApiForEditFolder(
                                    context: context,
                                    folderId: folder!.id,
                                    folderName: controller
                                        .folderNameController.value.text
                                        .trim(),
                                  );
                                } else {
                                  controller.callApiForCreateFolder(
                                    context: context,
                                    folderName: controller
                                        .folderNameController.value.text
                                        .trim(),
                                  );
                                }
                              } else {
                                CommonFunction.showCustomSnackbar(
                                  message: "Please enter folder name",
                                  backgroundColor: AppTheme.red,
                                  isError: true,
                                );
                              }
                            }),
                      ],
                    ),
                    Space.height(30),
                  ],
                );
              }),
            ),
          ],
        ),
      ),
    ),
  );
}

showFolderOption(ProjectFolder folder, {bool isDeleteVisible = false,required ProjectDetailViewController controller}) {
  return Column(
    children: [
      Space.height(40),
      InkWell(
        onTap: () {
          Get.back();
          controller.folderNameController.text = folder.name;
          showModalBottomSheet(
            context: Get.context!,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(40)),
            ),
            builder: (context) =>
                createOrEditFolderBottomSheet(context,
                    isEdit: true, folder: folder, controller: controller),
          ).then(
                (value) {
              controller.folderNameController.clear();
            },
          );
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.editIcon,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.whiteWithBase,
            ),
            Space.width(20),
            TypoGraphy(
              text: "Rename",
              level: 5,
              // color: AppTheme.baseBlack,
            )
          ],
        ),
      ),
      Space.height(41),
      if (isDeleteVisible) ...[
        InkWell(
          onTap: () {
            Navigator.pop(Get.context!);
            HapticFeedback.heavyImpact();
            showDeleteConfirmationDialog(
              context: Get.context!,
              description:
              "Are you sure you want to delete Folder permanently ?",
              onConfirm: () async {
                controller.callApiForDeleteFolder(folderId: folder.id);
              },
              title: "Delete Folder",
              onCancel: () {
                Get.back();
              },
            );
          },
          child: Row(
            children: [
              Space.width(30),
              SvgPicture.asset(
                AppImage.trashIcon,
                height: MySize.size24,
                width: MySize.size24,
              ),
              Space.width(20),
              TypoGraphy(
                text: "Delete",
                level: 5,
                color: AppTheme.red,
              )
            ],
          ),
        ),
        Space.height(41),
      ]
    ],
  );
}