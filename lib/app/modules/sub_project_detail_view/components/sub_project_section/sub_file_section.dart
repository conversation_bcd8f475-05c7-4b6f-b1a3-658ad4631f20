import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../../constants/app_image.dart';
import '../../../../../constants/app_size_constant.dart';
import '../../../../../main.dart';
import '../../../../../models/app_project_model.dart';
import '../../../../../models/section_base.dart';
import '../../../../../utillites/app_text_field.dart';
import '../../../../../utillites/app_theme.dart';
import '../../../../../utillites/buttons.dart';
import '../../../../../utillites/common_function.dart';
import '../../../../../utillites/common_grid_view.dart';
import '../../../../../utillites/common_shimmer_grid_view.dart';
import '../../../../../utillites/common_validation.dart';
import '../../../../../utillites/current_user.dart';
import '../../../../../utillites/delete_confirmation_dialog.dart';
import '../../../../../utillites/empty.dart';
import '../../../../../utillites/typography.dart';
import '../../../project_detail_view/views/project_folder_detail_view.dart';
import '../../../user_detail/components/image_picker_bottom_sheet.dart';
import '../../controllers/sub_project_detail_view_controller.dart';

class FileSection extends SectionBase<SubProjectDetailViewController> {
  FileSection({required super.controller});

  @override
  String get title => 'Files';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return FloatingActionButton(
      backgroundColor: AppTheme.primary1,
      // shape: const CircleBorder(),
      onPressed: () async {
        HapticFeedback.lightImpact();
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius:
            BorderRadius.vertical(top: Radius.circular(40)),
          ),
          builder: (context) =>
              createOrEditFolderBottomSheet(context,controller: controller),
        ).then(
              (value) {
            controller.folderNameController.clear();
          },
        );
        return;
      },
      child: SvgPicture.asset(
        controller.iconMap[controller.currentSelectedIndex.value] ??
            AppImage.postIcon,
        height: MySize.size70,
      ),
    );
  }


  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      SliverToBoxAdapter(
        child: Padding(
          padding: EdgeInsets.only(
              left: MySize.size30 ?? 30,
              right: MySize.size30 ?? 30,
              top: MySize.size25 ?? 30),
          child: Obx(
                () => controller.isFoldersLoading.value &&
                controller.folderList.isEmpty
                ? ShimmerGridView()
                : controller.folderList.isNotEmpty
                ? GridView.builder(
              padding: EdgeInsets.zero,
              keyboardDismissBehavior:
              ScrollViewKeyboardDismissBehavior
                  .onDrag,
              gridDelegate:
              FixedHeightGridDelegate(
                itemHeight:
                MySize.size220 ?? 200,
                crossAxisCount: 2,
                crossAxisSpacing:
                MySize.size20 ?? 20,
                mainAxisSpacing:
                MySize.size10 ?? 30,
              ),
              physics: ClampingScrollPhysics(),
              shrinkWrap: true,
              controller: ScrollController(),
              itemCount:
              controller.folderList.length,
              itemBuilder: (context, index) {
                var folder =
                controller.folderList[index]
                as ProjectFolder;
                return GestureDetector(
                  onTap: () {
                    Get.to(
                          () =>
                          ProjectFolderDetailView(
                            folder: folder,
                            isIconVisible: CurrentUser
                                .user.id ==
                                controller
                                    .getProjectDetailData
                                    .value
                                    .userId ||
                                (controller
                                    .getProjectDetailData
                                    .value
                                    .projectMembers
                                    .isNotEmpty &&
                                    controller
                                        .getProjectDetailData
                                        .value
                                        .projectMembers[
                                    0]
                                        .access ==
                                        "write"),
                            isDeleteVisible: CurrentUser
                                .user.id ==
                                controller
                                    .getProjectDetailData
                                    .value
                                    .userId,
                          ),
                    )?.then(
                          (value) {
                        controller
                            .callApiForGetFolders(
                            context:
                            context);
                      },
                    );
                  },
                  child: Column(
                    crossAxisAlignment:
                    CrossAxisAlignment
                        .start,
                    children: [
                      Stack(
                        children: [
                          Container(
                            height: MySize
                                .getScaledSizeHeight(
                                142),
                            width: MySize
                                .safeWidth,
                            decoration:
                            BoxDecoration(
                              color: box.read('isDarkMode') ? AppTheme.darkBackground : Color(0xfff6f6f6),
                              // color: Color(
                              //     0xfff6f6f6),
                              border: Border.all(color: AppTheme.borderWithTrans),
                              borderRadius:
                              BorderRadius
                                  .circular(
                                  25),
                            ),
                            child: Center(
                              child:
                              Image.asset(
                                AppImage.folder,
                                height: MySize
                                    .size90,
                                width: MySize
                                    .size90,
                                color: AppTheme.whiteWithNull,
                              ),
                            ),
                          ),
                          if ((CurrentUser.user
                              .id ==
                              controller
                                  .getProjectDetailData
                                  .value
                                  .userId || CurrentUser.user
                              .id == controller.folderList[index].userId) ||
                              (controller
                                  .getProjectDetailData
                                  .value
                                  .projectMembers
                                  .isNotEmpty &&
                                  controller
                                      .getProjectDetailData
                                      .value
                                      .projectMembers[
                                  0]
                                      .access ==
                                      "write"))
                            Positioned(
                              top:
                              MySize.size12,
                              right:
                              MySize.size12,
                              child:
                              GestureDetector(
                                onTap: () {
                                  HapticFeedback
                                      .lightImpact();
                                  ImagePickerBottomSheet
                                      .show(
                                    context:
                                    context,
                                    child: showFolderOption(
                                        folder,
                                        isDeleteVisible:
                                        CurrentUser.user.id == controller.getProjectDetailData.value.userId ||
                                            CurrentUser.user.id == controller.folderList[index].userId, controller: controller),
                                  );
                                },
                                child:
                                SvgPicture
                                    .asset(
                                  AppImage
                                      .moreVertIcon,
                                  color:
                                  AppTheme
                                      .grey,
                                ),
                              ),
                            ),
                        ],
                      ),
                      Space.height(6),
                      TypoGraphy(
                        text: folder.name,
                        level: 12,
                      ),
                      Space.height(4),
                      TypoGraphy(
                        text:
                        '${folder.filesCount} Files',
                        color:  box.read('isDarkMode')
                            ? AppTheme.grey : AppTheme.baseBlack,
                        level: 2,
                      ),
                    ],
                  ),
                );
              },
            )
                : Padding(
              padding: EdgeInsets.only(
                  top: MySize.size75 ?? 50),
              child: const Empty(
                title: "No Files available!",
              ),
            ),
          ),
        ),
      ),
      SliverToBoxAdapter(child: Space.height(35)),
    ];
  }

  @override
  void onCategorySelected() {
    print("api calling ==>");
    controller.page.value = 1;
    controller.hasMoreData.value =
    true;
    controller.folderList.clear();
    controller.callApiForGetFolders(
        context: Get.context!);
  }


}

showFolderOption(ProjectFolder folder, {bool isDeleteVisible = false,required SubProjectDetailViewController controller}) {
  return Column(
    children: [
      Space.height(40),
      InkWell(
        onTap: () {
          Get.back();
          controller.folderNameController.text = folder.name;
          showModalBottomSheet(
            context: Get.context!,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(40)),
            ),
            builder: (context) => createOrEditFolderBottomSheet(context,
                isEdit: true, folder: folder,controller: controller),
          ).then(
                (value) {
              controller.folderNameController.clear();
            },
          );
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.editIcon,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.whiteWithBase,
            ),
            Space.width(20),
            TypoGraphy(
              text: "Rename",
              level: 5,
              // color: AppTheme.baseBlack,
            )
          ],
        ),
      ),
      Space.height(41),
      if (isDeleteVisible) ...[
        InkWell(
          onTap: () {
            Navigator.pop(Get.context!);
            HapticFeedback.heavyImpact();
            showDeleteConfirmationDialog(
              context: Get.context!,
              description:
              "Are you sure you want to delete Folder permanently?",
              onConfirm: () async {
                controller.callApiForDeleteFolder(folderId: folder.id);
              },
              title: "Delete Folder",
              onCancel: () {
                Get.back();
              },
            );
          },
          child: Row(
            children: [
              Space.width(30),
              SvgPicture.asset(
                AppImage.trashIcon,
                height: MySize.size24,
                width: MySize.size24,
              ),
              Space.width(20),
              TypoGraphy(
                text: "Delete",
                level: 5,
                color: AppTheme.red,
              )
            ],
          ),
        ),
        Space.height(41),
      ]
    ],
  );
}

Widget createOrEditFolderBottomSheet(BuildContext context,
    {bool isEdit = false, ProjectFolder? folder,required SubProjectDetailViewController controller}) {
  return BackdropFilter(
    filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
    child: Container(
      decoration: BoxDecoration(
        color: AppTheme.appBottomSheet,
        border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(40),
            topRight: Radius.circular(40)),
      ),
      child: Padding(
        padding:
        EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Space.height(8),
            Container(
              height: MySize.size5,
              width: MySize.size34,
              decoration: BoxDecoration(
                color: box.read('isDarkMode') ? AppTheme.grey : AppTheme.black.withOpacity(0.2),
                borderRadius: BorderRadius.circular(100),
              ),
            ),
            Space.height(30),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
              child: Align(
                alignment: Alignment.centerRight,
                child: InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () {
                    Get.back();
                  },
                  child: SvgPicture.asset(
                    AppImage.closeImage,
                    height: MySize.size16,
                    width: MySize.size16,
                    color: AppTheme.whiteWithNull,
                  ),
                ),
              ),
            ),
            Space.height(10),
            Align(
              alignment: Alignment.center,
              child: TypoGraphy(
                text: 'New Folder',
                level: 8,
                fontWeight: FontWeight.w700,
              ),
            ),
            Space.height(10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
              child: StatefulBuilder(builder: (context, setTextState) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Space.height(20),
                    AppTextField(
                      controller: controller.folderNameController,
                      labelText: "Folder Name",
                      maxLength: 30,
                      textCapitalization: TextCapitalization.words,
                      onChangedValue: (p0) {
                        setTextState(() {});
                      },
                      validators: [
                            (value) => CommonValidation.isEmpty(value: value ?? ''),
                      ],
                    ),
                    Space.height(30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Buttons(
                            isDisable: controller.folderNameController.text
                                .trim()
                                .isEmpty,
                            disableColor:
                            AppTheme.primary1.withValues(alpha: 0.5),
                            textDisableColor: AppTheme.white,
                            width: MySize.size198,
                            buttonText: isEdit ? "Update" : "Create",
                            buttonTextLevel: 4,
                            onTap: () {
                              if (controller.folderNameController.value.text
                                  .trim()
                                  .isNotEmpty) {
                                if (isEdit) {
                                  controller.callApiForEditFolder(
                                    context: context,
                                    folderId: folder!.id,
                                    folderName: controller
                                        .folderNameController.value.text
                                        .trim(),
                                  );
                                } else {
                                  controller.callApiForCreateFolder(
                                    context: context,
                                    folderName: controller
                                        .folderNameController.value.text
                                        .trim(),
                                  );
                                }
                              } else {
                                CommonFunction.showCustomSnackbar(
                                  message: "Please enter folder name",
                                  backgroundColor: AppTheme.red,
                                  isError: true,
                                );
                              }
                            }),
                      ],
                    ),
                    Space.height(30),
                  ],
                );
              }),
            ),
          ],
        ),
      ),
    ),
  );
}
