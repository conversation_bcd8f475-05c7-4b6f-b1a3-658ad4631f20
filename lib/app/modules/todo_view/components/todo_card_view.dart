
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:incenti_ai/utillites/file_downloader.dart';
import 'package:intl/intl.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/custom_slidable.dart';
import '../../../../utillites/delete_confirmation_dialog.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../controllers/todo_view_controller.dart';

Widget toDoCard({required TodoViewController controller, required int index}) {
  RxBool isExpand = false.obs;
  return Slidable(
    dragStartBehavior: DragStartBehavior.start,
    key: ValueKey(controller.todoList[index].id),
    endActionPane: ActionPane(
      extentRatio: 0.65,
      motion: StretchMotion(),
      children: [
        SlidableActionPadding(
          borderRadius: BorderRadius.circular(20),
          onPressed: (context) {
            if (controller.todoList[index].userId != CurrentUser.user.id) {
              // Get.snackbar(
              //   "Permission Denied",
              //   "You are not allowed to edit this todo.",
              //   snackPosition: SnackPosition.BOTTOM,
              //   backgroundColor: AppTheme.red,
              //   colorText: AppTheme.white,
              // );
              CommonFunction.showCustomSnackbar(
                  message: "You are not allowed to edit this todo.",
                  backgroundColor: AppTheme.red,
                  isError: true);
              return;
            } else {
              HapticFeedback.lightImpact();
              Get.toNamed(Routes.create_todo, arguments: {
                "isBottom": true,
                "isEdit": true,
                "todoId": controller.todoList[index].id.toString(),
              })?.then(
                (value) {
                  controller.page.value = 1;
                  controller.hasMoreData.value = true;
                  controller.todoList.clear();
                  controller.callApiForToDo(context: context);
                },
              );
            }
          },
          backgroundColor:
              controller.todoList[index].userId != CurrentUser.user.id
                  ? AppTheme.lightGrey
                  : Color(0xFF0fbe5b),
          foregroundColor: Colors.white,
          icon: SvgPicture.asset(
            AppImage.editTodo,
            color: controller.todoList[index].userId != CurrentUser.user.id
                ? AppTheme.baseBlack
                : null,
          ),
          label: Padding(
            padding: EdgeInsets.only(top: 8.0),
            child: TypoGraphy(
              text: "Edit",
              level: 3,
              color: controller.todoList[index].userId != CurrentUser.user.id
                  ? AppTheme.baseBlack
                  : AppTheme.white,
            ),
          ),
        ),
        SlidableActionPadding(
          borderRadius: BorderRadius.circular(20),
          onPressed: (context) {
            if (controller.todoList[index].userId != CurrentUser.user.id) {
              CommonFunction.showCustomSnackbar(
                  message: "You are not allowed to delete this todo.",
                  isError: true,
                  backgroundColor: AppTheme.red);
              return;
            } else {
              HapticFeedback.heavyImpact();
              showDeleteConfirmationDialog(
                context: Get.context!,
                description:
                    "Are you sure you want to delete todo permanently?",
                onConfirm: () async {
                  controller
                      .callApiForDeleteOneToDo(
                          context: Get.context!,
                          todoId: controller.todoList[index].id.toString())
                      .then(
                    (value) {
                      controller.page.value = 1;
                      controller.hasMoreData.value = true;
                      controller.todoList.clear();
                      controller.callApiForToDo(context: Get.context!);
                    },
                  );
                },
                title: "Delete Todo",
                onCancel: () {
                  Get.back();
                },
              );
            }
          },
          backgroundColor:
              controller.todoList[index].userId != CurrentUser.user.id
                  ? AppTheme.lightGrey
                  : Color(0xFFFE4A49),
          foregroundColor: Colors.white,
          icon: SvgPicture.asset(
            AppImage.deleteTodo,
            color: controller.todoList[index].userId != CurrentUser.user.id
                ? AppTheme.red
                : null,
          ),
          label: Padding(
            padding: EdgeInsets.only(top: 8.0),
            child: TypoGraphy(
              text: "Delete",
              level: 3,
              color: controller.todoList[index].userId != CurrentUser.user.id
                  ? AppTheme.red
                  : AppTheme.white,
            ),
          ),
        ),
      ],
    ),
    child: Row(
      children: [
        Obx(
          () => InkWell(
            onTap: () {
              HapticFeedback.lightImpact();
              controller
                  .callApiForCompleteToDo(
                      context: Get.context!,
                      index: index,
                      todoId: controller.todoList[index].id.toString());
            },
            child: Container(
              height: MySize.getScaledSizeHeight(25),
              width: MySize.getScaledSizeWidth(25),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: (controller.todoList[index].status?.value ?? false)
                    ? AppTheme.success
                    : Colors.transparent,
                border: Border.all(
                  color: (controller.todoList[index].status?.value ?? false)
                      ? AppTheme.success
                      : AppTheme.baseBlack,
                ),
              ),
              child: (controller.todoList[index].status?.value ?? false)
                  ? Padding(
                      padding: EdgeInsets.all(5),
                      child: SvgPicture.asset(AppImage.checkIcon,
                          color: AppTheme.white),
                    )
                  : null,
            ),
          ),
        ),
        Space.width(15.11),
        Expanded(
          child: Obx(
            () => InkWell(
              onTap: () {
                isExpand.value = !isExpand.value;
              },
              child: IntrinsicHeight(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(25),
                  child: Stack(
                    children: [
                      Container(
                        // width: double.infinity,
                        padding: EdgeInsets.symmetric(
                                vertical: MySize.getScaledSizeHeight(25))
                            .copyWith(left: MySize.getScaledSizeWidth(26)),
                        decoration: BoxDecoration(
                          color: Theme.of(Get.context!).brightness ==
                                  Brightness.dark
                              ? AppTheme.bottomBar
                              : AppTheme.lightGrey,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TypoGraphy(
                              text: controller.todoList[index].name,
                              level: 5,
                              fontWeight: FontWeight.w600,
                              color:
                                  (controller.todoList[index].status?.value ??
                                          false)
                                      ? AppTheme.grey
                                      : null,
                            ),
                            if (isExpand.value &&
                                (controller.todoList[index].description ?? "")
                                    .isNotEmpty) ...[
                              Space.height(10),
                              TypoGraphy(
                                text: controller.todoList[index].description,
                                level: 4,
                                fontWeight: FontWeight.w400,
                                color:
                                    (controller.todoList[index].status?.value ??
                                            false)
                                        ? AppTheme.grey
                                        : null,
                              ),
                            ],
                            if (isExpand.value &&
                                (controller.todoList[index].files ?? [])
                                    .isNotEmpty) ...[
                              Space.height(10),
                              Column(
                                children: controller.todoList[index].files!
                                    .map((file) {
                                  final fileName =
                                      file.name?.toLowerCase() ?? '';
                                  final isPdf = fileName.endsWith('.pdf');

                                  return Padding(
                                    padding: EdgeInsets.only(
                                        bottom: MySize.getScaledSizeHeight(15)),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SvgPicture.asset(
                                          isPdf
                                              ? AppImage.fileTypePdf
                                              : AppImage.docImage,
                                          width: MySize.getScaledSizeWidth(20),
                                          height:
                                              MySize.getScaledSizeHeight(26.61),
                                        ),
                                        Space.width(10),
                                        Expanded(
                                          child: TypoGraphy(
                                            text: file.name ?? '',
                                            level: 3,
                                            fontWeight: FontWeight.w400,
                                            color: (controller.todoList[index]
                                                        .status?.value ??
                                                    false)
                                                ? AppTheme.grey
                                                : null,
                                          ),
                                        ),
                                        Space.width(10),
                                        InkWell(
                                          onTap: () async {
                                            controller.downloadingFileIds
                                                .add(file.id);
                                            await FileDownloader.downloadFile(
                                                file.link!, file.name ?? '');
                                            controller.downloadingFileIds
                                                .remove(file.id);
                                          },
                                          child: Row(
                                            children: [
                                              Obx(() {
                                                final isDownloading = controller
                                                    .downloadingFileIds
                                                    .contains(file.id);
                                                return isDownloading
                                                    ? SizedBox.square(
                                                        dimension:
                                                            MySize.size20,
                                                        child:
                                                            CircularProgressIndicator
                                                                .adaptive(
                                                          valueColor:
                                                              AlwaysStoppedAnimation(
                                                                  AppTheme
                                                                      .primaryIconDark),
                                                          strokeWidth:
                                                              MySize.size2 ?? 2,
                                                          strokeCap:
                                                              StrokeCap.round,
                                                        ),
                                                      )
                                                    : SvgPicture.asset(
                                                        AppImage.downloadIcon,
                                                  color: AppTheme.primaryIconDark,
                                                        height: MySize
                                                            .getScaledSizeHeight(
                                                                15),
                                                      );
                                              }),
                                              Space.width(9),
                                              TypoGraphy(
                                                text: "Download",
                                                level: 3,
                                                fontWeight: FontWeight.w500,
                                                color: AppTheme.primaryIconDark,
                                              ),
                                            ],
                                          ),
                                        ),
                                        Space.width(20),
                                      ],
                                    ),
                                  );
                                }).toList(),
                              ),
                            ],
                            Space.height(10),
                            Row(
                              children: [
                                if (controller.todoList[index].dueDate !=
                                    null) ...[
                                  TypoGraphy(
                                    text:
                                        "${DateFormat("MMM dd,yyyy").format(controller.todoList[index].dueDate ?? DateTime.now())} • ",
                                    level: 2,
                                    fontWeight: FontWeight.w400,
                                    color: AppTheme.grey,
                                  ),
                                ],
                                if (controller.todoList[index].priority !=
                                    null) ...[
                                  TypoGraphy(
                                    text: controller.todoList[index].priority ==
                                            1
                                        ? "Low"
                                        : controller.todoList[index].priority ==
                                                2
                                            ? "Medium"
                                            : "High",
                                    level: 2,
                                    fontWeight: FontWeight.w400,
                                    color: AppTheme.grey,
                                  ),
                                ],
                                if (controller.todoList[index].priority !=
                                        null &&
                                    controller.todoList[index].project !=
                                        null) ...[
                                  TypoGraphy(
                                    text: " • ",
                                    level: 2,
                                    fontWeight: FontWeight.w400,
                                    color: AppTheme.grey,
                                  ),
                                ],
                                if (controller.todoList[index].project !=
                                    null) ...[
                                  Expanded(
                                    child: TypoGraphy(
                                      text: controller.todoList[index].project
                                                  ?.parentProjectData !=
                                              null
                                          ? "Project: ${controller.todoList[index].project?.parentProjectData?.name}"
                                          : "Project: ${controller.todoList[index].project?.name}",
                                      level: 2,
                                      fontWeight: FontWeight.w400,
                                      color: AppTheme.grey,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                            if (controller.todoList[index].userId !=
                                CurrentUser.user.id) ...[
                              Space.height(10),
                              Row(
                                children: [
                                  TypoGraphy(
                                    text: "Assigned by: ",
                                    level: 2,
                                    fontWeight: FontWeight.w400,
                                    color: AppTheme.grey,
                                  ),
                                  profileImage(
                                      url: controller
                                              .todoList[index].user?.image ??
                                          "",
                                      userName: controller.todoList[index].user
                                              ?.firstName ??
                                          "",
                                      iconHeight:
                                          MySize.getScaledSizeHeight(16),
                                      iconWidth: MySize.getScaledSizeWidth(16),
                                      height: MySize.getScaledSizeHeight(16),
                                      width: MySize.getScaledSizeWidth(16),
                                      textStyle: TextStyle(
                                          fontSize: 8, color: AppTheme.white)),
                                  TypoGraphy(
                                    text:
                                        " ${controller.todoList[index].user?.firstName} ${controller.todoList[index].user?.lastName}",
                                    level: 2,
                                    fontWeight: FontWeight.w500,
                                    color: (controller.todoList[index].status
                                                ?.value ??
                                            false)
                                        ? AppTheme.grey
                                        : Theme.of(Get.context!).brightness ==
                                                Brightness.dark
                                            ? AppTheme.grey[50]
                                            : null,
                                  ),
                                ],
                              ),
                            ],
                            if (controller.todoList[index].assignedUser !=
                                null) ...[
                              Space.height(10),
                              Row(
                                children: [
                                  TypoGraphy(
                                    text: "Assigned to: ",
                                    level: 2,
                                    fontWeight: FontWeight.w400,
                                    color: AppTheme.grey,
                                  ),
                                  profileImage(
                                    url: controller.todoList[index].assignedUser
                                            ?.image ??
                                        "",
                                    userName: controller.todoList[index]
                                            .assignedUser?.firstName ??
                                        "",
                                    iconHeight: MySize.getScaledSizeHeight(16),
                                    iconWidth: MySize.getScaledSizeWidth(16),
                                    height: MySize.getScaledSizeHeight(16),
                                    width: MySize.getScaledSizeWidth(16),
                                    textStyle: TextStyle(
                                        fontSize: 8, color: AppTheme.white),
                                  ),
                                  TypoGraphy(
                                    text:
                                        " ${controller.todoList[index].assignedUser?.firstName} ${controller.todoList[index].assignedUser?.lastName}",
                                    level: 2,
                                    fontWeight: FontWeight.w500,
                                    color: (controller.todoList[index].status
                                                ?.value ??
                                            false)
                                        ? AppTheme.grey
                                        : Theme.of(Get.context!).brightness ==
                                                Brightness.dark
                                            ? AppTheme.grey[50]
                                            : null,
                                  ),
                                ],
                              ),
                            ],
                            Space.height(10),
                            if (controller.todoList[index].userId ==
                                CurrentUser.user.id) ...[
                              Row(
                                children: [
                                  TypoGraphy(
                                    text: "Created by me",
                                    level: 2,
                                    fontWeight: FontWeight.w400,
                                    color: AppTheme.grey,
                                  ),
                                  // profileImage(
                                  //     url: controller
                                  //             .todoList[index].user?.image ??
                                  //         "",
                                  //     userName: controller.todoList[index].user
                                  //             ?.firstName ??
                                  //         "",
                                  //     iconHeight:
                                  //         MySize.getScaledSizeHeight(16),
                                  //     iconWidth: MySize.getScaledSizeWidth(16),
                                  //     height: MySize.getScaledSizeHeight(16),
                                  //     width: MySize.getScaledSizeWidth(16),
                                  //     textStyle: TextStyle(
                                  //         fontSize: 8, color: AppTheme.white)),
                                  // TypoGraphy(
                                  //   text:
                                  //       " ${controller.todoList[index].user?.firstName} ${controller.todoList[index].user?.lastName}",
                                  //   level: 2,
                                  //   fontWeight: FontWeight.w500,
                                  //   color: (controller.todoList[index].status
                                  //               ?.value ??
                                  //           false)
                                  //       ? AppTheme.grey
                                  //       : Theme.of(Get.context!).brightness ==
                                  //               Brightness.dark
                                  //           ? AppTheme.grey[50]
                                  //           : null,
                                  // ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                      Container(
                        width: MySize.getScaledSizeWidth(6),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(colors: List.generate(3, (i){
                            return (controller.todoList[index].status?.value ?? false) ? AppTheme.baseBlack : (controller.todoList[index].priority == 1
                                ? AppTheme.success
                                : controller.todoList[index].priority == 2
                                ? AppTheme.yellow
                                : AppTheme.red).withValues(alpha: 1 - i * 0.3);
                          })),
                          boxShadow: [BoxShadow(
                            color:  (controller.todoList[index].status?.value ?? false) ? AppTheme.baseBlack : (controller.todoList[index].priority == 1
                                ? AppTheme.success
                                : controller.todoList[index].priority == 2
                                ? AppTheme.yellow
                                : AppTheme.red).withValues(alpha: 0.7),
                            spreadRadius: 2,
                            blurRadius: 5,
                          )],
                          backgroundBlendMode: BlendMode.color
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    ),
  );
}
