import 'dart:async';
import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../../../constants/api.dart';
import '../../../../constants/constant.dart';
import '../../../../main.dart';
import '../../../../models/app_comment_model.dart';
import '../../../../models/app_community_model.dart';
import '../../../../models/app_other_user_model.dart';
import '../../../../models/app_post_model.dart';
import '../../../../models/app_project_model.dart';
import '../../../../models/app_team_user_model.dart';
import '../../../../models/post_like_model.dart';
import '../../../../models/section_base.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/common_function.dart';
import '../components/global_search_community_section.dart';
import '../components/global_search_post_section.dart';
import '../components/global_search_project_section.dart';
import '../components/global_search_user_section.dart';

class GlobalSearchViewController extends GetxController {
  Rx<TextEditingController> searchController = TextEditingController().obs;
  FocusNode searchFocusNode = FocusNode();
  RxInt currentSelectedIndex = 0.obs;
  RxBool isExploreLoading = false.obs;
  ApiManager apiManager = ApiManager();
  RxList<Post> postDataList = <Post>[].obs;
  int limit = 20;
  RxBool hasMoreData = true.obs;
  RxBool isLoading = false.obs;
  RxBool isCommunityLoading = false.obs;
  RxBool isProjectLoading = false.obs;
  RxBool isJoinLoading = false.obs;
  RxList<PostLikeData> postLikeList = <PostLikeData>[].obs;
  List<SectionBase> sections = [];
  TextEditingController commentController = TextEditingController();
  RxList<Comment> commentDataList = <Comment>[].obs;
  FocusNode commentFocusNode = FocusNode();
  Timer? debounceTimer;
  RxInt page = 1.obs;
  RxInt projectPage = 1.obs;
  RxList<Project> createdProject = <Project>[].obs;
  ScrollController scrollController = ScrollController();
  RxList<OtherUser> projectUserList = <OtherUser>[].obs;
  RxList<CommunityData> communityCreatedList = <CommunityData>[].obs;
  RxBool isFollowing = false.obs;
  RxString selectedReason = "".obs;
  RxInt totalCount = 0.obs;
  RxList repostData = [
    "I just don't Like it",
    "Scam, fraud or spam",
    "False information",
  ].obs;

  @override
  void onInit() {
    super.onInit();
    callApiForExplorePost(context: Get.context!);
    scrollController.addListener(
          () => onScrollListener(() {
            projectPage.value++;
        fetchProjectData(
            context: Get.context!,);
      }, scrollController),
    );
    sections = [
      GlobalSearchPostSection(controller: this),
      GlobalSearchProjectSection(controller: this),
      GlobalSearchUserSection(controller: this),
      GlobalSearchCommunitySection(controller: this),
    ];
  }

  callApiForReportProject(
      {required BuildContext context, required String projectId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
    ApiModel("/reported-projects/project/$projectId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            CommonFunction.showCustomSnackbar(message: response['message']);
            projectPage.value = 1;
            hasMoreData.value = true;
            createdProject.clear();
            fetchProjectData(
              context: Get.context!,);
          }
        } catch (error) {
        }
      },
      failureCallback: (message, statusCode) {
      },
    );
  }


  void onScrollListener(void Function() onScroll, ScrollController controller) {
    if (!hasMoreData.value) return;

    // Use a threshold for safer scroll detection
    if (controller.position.pixels >=
        controller.position.maxScrollExtent - 200) {
      onScroll();
    }
  }

  callApiForReportPost(
      {required BuildContext context, required String postId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
    ApiModel("/reported-posts/post/$postId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(message: response['message']);
            page.value = 1;
            hasMoreData.value = true;
            postDataList.clear();
            callApiForExplorePost(context: Get.context!);
          }
        } catch (error) {
        }
      },
      failureCallback: (message, statusCode) {
      },
    );
  }

  Future<void> callApiForGetTeamUser({required BuildContext context}) {
    isLoading.value = true;

    return apiManager.callApi(
      APIS.projectMember.getAllTeamMember,
      params: {
        "searchQuery": searchController.value.text,
        "limit": limit,
        "page": page.value,
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            TeamUserResponse teamUserResponse =
            TeamUserResponse.fromJson(response);
            final newData = teamUserResponse.data;
            final isLastPage = newData.length < limit;
            hasMoreData.value = !isLastPage;

            // if (page.value == 1) {
            //   projectUserList.value = newData;
            // } else {
            final existingIds = projectUserList.map((e) => e.id).toSet();
            final filteredData = newData.where((user) => !existingIds.contains(user.id)).toList();
            totalCount.value = teamUserResponse.totalRecords;
            projectUserList.addAll(filteredData);
            // }

            page.value++;
          }
        } catch (error) {
          hasMoreData.value = false;
        } finally {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        hasMoreData.value = false;
        isLoading.value = false;
      },
    );
  }


  Future<void> callApiForUnFollowUser(
      {required BuildContext context,
        required int? userId,
        required bool isUnFollowed,
        required int index}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
    ApiModel("/friends/user/$userId", APIType.DELETE);
    isFollowing.value = false;
    log('api called for =====> unfollow user');

    return apiManager.callApi(
      updatePost,
      params: {if (isUnFollowed) "unfollow": 1},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowing.value = false;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            projectUserList[index].isFollowing?.value = true;
            Navigator.pop(context);
          }
        } catch (error) {
          isFollowing.value = true;
        }
      },
      failureCallback: (message, statusCode) {
        isFollowing.value = true;
      },
    );
  }


  Future<void> callApiForFollowUser(
      {required BuildContext context, required int? userId}) {
    log('api called for =====> follow user');

    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
    ApiModel("/friends/follow/user/$userId", APIType.POST);
    isFollowing.value = true;
    return apiManager.callApi(
      updatePost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowing.value = true;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
          }
        } catch (error) {
          isFollowing.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isFollowing.value = false;
      },
    );
  }

  callApiForExplorePost({required BuildContext context}) async {
    // Prevent multiple simultaneous API calls
    if (isExploreLoading.value) return;
    isExploreLoading.value = true;
    // String? lastCreatedAt = postDataList.isNotEmpty
    //     ? postDataList.last.createdAt?.toIso8601String()
    //     : null;

    try {
      await apiManager.callApi(
        APIS.post.getAllExplorePost,
        params: {
          "limit": limit,
          "page": page.value,
          if (searchController.value.text.trim().isNotEmpty)
            "searchQuery": searchController.value.text
        },
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            PostResponse postResponse = PostResponse.fromJson(response);
            if (postResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            totalCount.value = postResponse.data.totalRecords;
            if (postResponse.data.data.isNotEmpty) {
              final existingIds = postDataList.map((e) => e.id).toSet();

              final newItems = postResponse.data.data.where((item) => !existingIds.contains(item.id));

              postDataList.addAll(newItems);
              if (box.read("newPost") != null) {
                postDataList.insert(0, box.read("newPost"));
                box.remove("newPost");
              }
              postDataList.refresh();
            }
            page.value++;
          }
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
        },
      );
    } catch (error) {
      hasMoreData.value = false;
    } finally {
      isExploreLoading.value = false;
    }
  }

  fetchProjectData({required BuildContext context}) {
    if (!hasMoreData.value) return;

    isProjectLoading.value = true;

    final params = {
      'page': projectPage.value,
      'limit': limit,
      if (searchController.value.text.isNotEmpty)
        "searchQuery": searchController.value.text,
    };

    apiManager.callApi(
      APIS.project.getAllMyProject,
      params: params,
      successCallback: (response, message) {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            ProjectResponse projectResponse = ProjectResponse.fromJson(response);

            if (projectResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            totalCount.value = projectResponse.data.totalRecords;
            if(projectResponse.data.data.isNotEmpty) {
              // final existingIds = createdProject.map((e) => e.id).toSet();
              //
              // final newItems = projectResponse.data.data.where((item) => !existingIds.contains(item.id));
              createdProject.addAll(projectResponse.data.data);
            }
          }
        } catch (error) {
          log("Error processing response: $error");
          hasMoreData.value = false;
        } finally {
          isProjectLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        log("Error: $message");
        isProjectLoading.value = false;
        hasMoreData.value = false;
      },
    );
  }

  Future<void> callApiForJoinCommunity(
      {required BuildContext context, required String communityId, required int index}) {
    FocusScope.of(context).unfocus();
    isJoinLoading.value = true;
    communityCreatedList[index].isJoined?.value = !(communityCreatedList[index].isJoined?.value ?? false);


    final ApiModel getPost = ApiModel("/community-members/$communityId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            isJoinLoading.value = false;
          }
        } catch (error) {
          log("error === $error");
          isJoinLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isJoinLoading.value = false;
      },
    );
  }


  Future<void> fetchCommunityData({required BuildContext context}) async {
    if (!hasMoreData.value) return;

    isCommunityLoading.value = true;

    try {
      final params = {
        "page": page.value,
        "limit": limit,
        if (searchController.value.text.isNotEmpty)
          "searchQuery": searchController.value.text.trim(),
      };

      await apiManager.callApi(
        APIS.communities.getCommunities,
        params: params,
        successCallback: (response, message) {
          if (response['status'] == 'success') {
            CommunityResponse communityResponse = CommunityResponse.fromJson(response);

            if (communityResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            totalCount.value = communityResponse.data.totalRecords;
            if(communityResponse.data.data.isNotEmpty) {
              final existingIds = communityCreatedList.map((e) => e.id).toSet();

              final newItems = communityResponse.data.data.where((item) => !existingIds.contains(item.id));
              communityCreatedList.addAll(newItems);
              page.value++;
            }
            isCommunityLoading.value = false;
          }
        },
        failureCallback: (message, statusCode) {
          log("Error: $message");
          hasMoreData.value = false;
          isCommunityLoading.value = false;
        },
      );
    } catch (e) {
      log("Exception: $e");
      hasMoreData.value = false;
    } finally {
      isCommunityLoading.value = false;
    }
  }


  callApiForLikeProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    postDataList[index].isLiked?.value =
        !(postDataList[index].isLiked?.value ?? false);
    if (postDataList[index].isLiked?.value == true) {
      postDataList[index].likesCount?.value++;
    } else {
      postDataList[index].likesCount?.value--;
    }
    final ApiModel getPost = ApiModel("/post-likes/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // postDataList[index].isLiked = (response["post"]["isLiked"] == "0" ? false : true).obs;
            // postDataList[index].likesCount = int.parse(response["post"]["likesCount"] ?? "0");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetCommentProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/post-comments/post/$postId", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommentResponse commentResponse =
                CommentResponse.fromJson(response);
            commentDataList.value = commentResponse.data.data;
            postDataList[index].commentsCount?.value = commentDataList.length;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForCommentProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/post-comments/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      params: {"comment": commentController.value.text.trim()},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            callApiForGetCommentProject(
                context: Get.context!, postId: postId, index: index);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForBookMarkProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    postDataList[index].isBookMarked?.value =
        !(postDataList[index].isBookMarked?.value ?? false);
    final ApiModel getPost = ApiModel("/bookmarks/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // postDataList[index].isBookMarked = response["post"]["isBookMarked"] == "0" ? false : true;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForGetPostLike({required BuildContext context,required int postId}) async {
    isLoading.value = true;
    FocusScope.of(context).unfocus();
    String? lastCreatedAt = postLikeList.isNotEmpty
        ? postLikeList.last.createdAt?.toIso8601String()
        : null;

    try {
      await apiManager.callApi(
        ApiModel("/post-likes/post/$postId", APIType.GET),
        params: {"limit": 20, if (lastCreatedAt != null) "createdAt": lastCreatedAt},
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            PostLike postLike = PostLike.fromJson(response);
            if ((postLike.data?.data?.length ?? 0) < 20) {
              hasMoreData.value = false;
            }
            if ((postLike.data?.data ?? []).isNotEmpty) {
              postLikeList.addAll(postLike.data?.data ?? []);
              postLikeList.refresh();
            }
          }
          isLoading.value = false;
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
          isLoading.value = false;

        },
      );
    } catch (error) {
      hasMoreData.value = false;
      isLoading.value = false;

    }
  }
}
