import 'dart:async';
import 'dart:developer';
import 'package:get/get.dart' hide Response, FormData, MultipartFile;
import 'package:flutter/material.dart';

import '../../../../constants/api.dart';
import '../../../../constants/constant.dart';
import '../../../../models/app_comment_model.dart';
import '../../../../models/app_community_model.dart';
import '../../../../models/app_member_model.dart';
import '../../../../models/app_post_model.dart';
import '../../../../models/post_like_model.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/common_function.dart';

class CommunityDetailViewController extends GetxController {
  Rx<TextEditingController> searchController = TextEditingController().obs;
  FocusNode searchFocusNode = FocusNode();
  Rx<CommunityData> getCommunityData = CommunityData().obs;
  ApiManager apiManager = ApiManager();
  var args = Get.arguments;
  RxInt communityId = (-1).obs;
  RxString communitySlug = ('').obs;
  RxInt index = (-1).obs;
  RxBool isCommunityLoading = false.obs;
  RxBool isCommunityPostLoading = false.obs;
  RxBool isFollowing = false.obs;
  RxBool isLoading = false.obs;
  RxList<Post> communityPostDataList = <Post>[].obs;
  RxList<PostLikeData> postLikeList = <PostLikeData>[].obs;

  RxBool hasMoreData = true.obs;
  int limit = 10;
  RxInt page = 1.obs;
  RxInt memberPage = 1.obs;
  RxString joiningDate = "".obs;
  TextEditingController commentController = TextEditingController();
  RxList<Comment> commentDataList = <Comment>[].obs;
  RxList<MemberData> memberDataList = <MemberData>[].obs;
  FocusNode commentFocusNode = FocusNode();
  Timer? debounceTimer;

  @override
  void onInit() {
    super.onInit();
    communitySlug.value = args != null && args["communitySlug"] != null
        ? args["communitySlug"]
        : "";
    if (args != null && args["communityId"] != null) {
      communityId.value = args["communityId"];
    }
    var parameter = communitySlug.value.isNotEmpty ? communitySlug.value : communityId.value.toString();
    callApiForGetOneCommunity(
        context: Get.context!, communityId: parameter);
    if (args != null && args["index"] != null) {
      index.value = args["index"];
    }
    if (communityId.value != -1) {
      callApiForGetCommunityPost(context: Get.context!);
    }
  }

  RxString selectedReason = "".obs;
  RxList repostData = [
    "I just don't Like it",
    "Scam, fraud or spam",
    "False information",
  ].obs;

  pullRefresh() {
    memberPage.value = 1;
    hasMoreData.value = true;
    callApiForGetCommunityMember(
        context: Get.context!,
        communityId: communityId.value.toString()
    );
  }

  callApiForReportPost(
      {required BuildContext context, required String postId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
    ApiModel("/reported-posts/post/$postId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(message: response['message']);
            page.value = 1;
            hasMoreData.value = true;
            communityPostDataList.clear();
            callApiForGetCommunityPost(context: Get.context!);
          }
        } catch (error) {
        }
      },
      failureCallback: (message, statusCode) {
      },
    );
  }

  callApiForReportCommunity(
      {required BuildContext context, required String communityId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
    ApiModel("/reported-communities/community/$communityId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Navigator.pop(context);
            CommonFunction.showCustomSnackbar(message: response['message']);
          }
        } catch (error) {
        }
      },
      failureCallback: (message, statusCode) {
      },
    );
  }

  Future<void> callApiForGetOneCommunity(
      {required BuildContext context, required String communityId}) {
    FocusScope.of(context).unfocus();
    isCommunityLoading.value = true;
    final ApiModel getPost = ApiModel("/communities/$communityId", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            getCommunityData.value = CommunityData.fromJson(response["data"]);
            joiningDate.value = (response["data"]["CommunityMembers"] as List).isNotEmpty ? response["data"]["CommunityMembers"][0]["createdAt"] : '';
            isCommunityLoading.value = false;
          }
        } catch (error) {
          log("error === $error");
          isCommunityLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isCommunityLoading.value = false;
      },
    );
  }

  Future<void> callApiForFollowUser(
      {required BuildContext context, required int? userId}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
        ApiModel("/friends/follow/user/$userId", APIType.POST);
    isFollowing.value = true;
    return apiManager.callApi(
      updatePost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowing.value = true;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
          }
        } catch (error) {
          isFollowing.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isFollowing.value = false;
      },
    );
  }

  Future<void> callApiForUnFollowUser(
      {required BuildContext context,
      required int? userId,
      required bool isUnFollowed,
      required int index}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
        ApiModel("/friends/user/$userId", APIType.DELETE);
    isFollowing.value = false;
    return apiManager.callApi(
      updatePost,
      params: {if (isUnFollowed) "unfollow": 1},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowing.value = false;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            memberDataList[index].user?.isFollowing?.value = true;
            Navigator.pop(context);
          }
        } catch (error) {
          isFollowing.value = true;
        }
      },
      failureCallback: (message, statusCode) {
        isFollowing.value = true;
      },
    );
  }

  // callApiForGetCommunityMember(
  //     {required BuildContext context, required String communityId}) {
  //   if (searchController.value.text.isNotEmpty) {
  //     // Cancel previous timer if exists
  //     debounceTimer?.cancel();
  //
  //     // Start new debounce timer (500ms delay) for searching
  //     debounceTimer = Timer(Duration(milliseconds: 500), () {
  //       memberDataList.clear();
  //       fetchCommunityMember(communityId: communityId, context: context);
  //     });
  //   } else {
  //     fetchCommunityMember(communityId: communityId, context: context);
  //   }
  // }

  Future<void> callApiForGetCommunityMember({
    required BuildContext context,
    required String communityId,
  }) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;

    final ApiModel getPost = ApiModel("/community-members/$communityId", APIType.GET);

    return apiManager.callApi(
      getPost,
      params: {
        if (searchController.value.text.isNotEmpty)
          "searchQuery": searchController.value.text,
        "limit": limit,
        "page": memberPage.value,
      },
      successCallback: (response, message) async {
        if (response['status'] == 'success') {
          log("response === $response");

          final memberResponse = MemberResponse.fromJson(response);
          final newData = memberResponse.members.data;
          final adminData = memberResponse.admin;
          final isLastPage = newData.length < limit;

          hasMoreData.value = !isLastPage;
          final adminId = adminData?.user?.id;
          final isAdminInResults = newData.any((m) => m.user?.id == adminId);

          if (memberPage.value == 1) {
            final isSearching = searchController.value.text.isNotEmpty;

            if (isSearching) {
              // 1. Search active
              if (newData.isEmpty && adminData != null) {
                // Only admin matched the search
                memberDataList.value = [adminData];
              } else if (adminData != null && !isAdminInResults) {
                // Admin matched but isn't in the list
                memberDataList.value = [adminData, ...newData];
              } else {
                // Admin already in results or is null
                memberDataList.value = newData;
              }
            } else {
              // 2. No search, always prepend admin if exists
              if (adminData != null) {
                memberDataList.value = [adminData, ...newData];
              } else {
                memberDataList.value = newData;
              }
            }
          } else {
            // Page > 1, load more members
            memberDataList.addAll(newData);
          }

          memberPage.value++;
        }
        isLoading.value = false;
      },
      failureCallback: (message, statusCode) {
        hasMoreData.value = false;
        isLoading.value = false;
      },
    );
  }


  Future<void> callApiForDeleteOneCommunity(
      {required BuildContext context, required String communityId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/communities/$communityId", APIType.DELETE);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Get.back();
            Get.back();
            update();
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetCommunityPost(
      {required BuildContext context}) async {
    FocusScope.of(context).unfocus();
    print("loading === $isCommunityPostLoading");
    print("dataList === $communityPostDataList");
    isCommunityPostLoading.value = true;
    // String? lastCreatedAt = communityPostDataList.isNotEmpty
    //     ? communityPostDataList.last.createdAt
    //         ?.toIso8601String() // Convert DateTime to String
    //     : null;

    return apiManager.callApi(
      APIS.post.getAllUsePost,
      params: {
        "limit": limit,
        "CommunityId": communityId.value,
       "page": page.value,
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            PostResponse postResponse = PostResponse.fromJson(response);
            if (postResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }

            if (postResponse.data.data.isNotEmpty) {
              communityPostDataList.addAll(postResponse.data.data);
              isCommunityPostLoading.value = false;
            } else {
              isCommunityPostLoading.value = false;
            }
            page.value++;
            log("response === $response");
          }
        } catch (error) {
          isCommunityPostLoading.value = false;
          hasMoreData.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isCommunityPostLoading.value = false;
        hasMoreData.value = false;
      },
    );
  }

  Future<void> callApiForDeleteCommunityPost(
      {required BuildContext context, required String communityId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/posts/$communityId", APIType.DELETE);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Get.back();
            update();
            isLoading.value = false;
          }
        } catch (error) {
          log("error === $error");
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForBookMarkProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    communityPostDataList[index].isBookMarked?.value =
        !(communityPostDataList[index].isBookMarked?.value ?? false);
    final ApiModel getPost = ApiModel("/bookmarks/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // postDataList[index].isBookMarked = response["post"]["isBookMarked"] == "0" ? false : true;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForLikeProject(
      {required BuildContext context,
      String? postId,
      required int index,
      isBookmark = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    communityPostDataList[index].isLiked?.value =
        !(communityPostDataList[index].isLiked?.value ?? false);
    if (communityPostDataList[index].isLiked?.value == true) {
      communityPostDataList[index].likesCount?.value++;
    } else {
      communityPostDataList[index].likesCount?.value--;
    }

    final ApiModel getPost = ApiModel("/post-likes/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // postDataList[index].isLiked = (response["post"]["isLiked"] == "0" ? false : true).obs;
            // postDataList[index].likesCount = int.parse(response["post"]["likesCount"] ?? "0");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }
  callApiForCommentProject(
      {required BuildContext context,
      String? postId,
      required int index,
      bool isBookMark = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/post-comments/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      params: {"comment": commentController.value.text.trim()},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            callApiForGetCommentProject(
                context: Get.context!,
                postId: postId,
                index: index,
                isBookMark: isBookMark);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForGetCommentProject(
      {required BuildContext context,
      String? postId,
      required int index,
      bool isBookMark = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/post-comments/post/$postId", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommentResponse commentResponse =
                CommentResponse.fromJson(response);
            commentDataList.value = commentResponse.data.data;
            communityPostDataList[index].commentsCount?.value =
                commentDataList.length;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForGetPostLike({required BuildContext context,required int postId}) async {
    isLoading.value = true;
    FocusScope.of(context).unfocus();
    String? lastCreatedAt = postLikeList.isNotEmpty
        ? postLikeList.last.createdAt?.toIso8601String()
        : null;

    try {
      await apiManager.callApi(
        ApiModel("/post-likes/post/$postId", APIType.GET),
        params: {"limit": 20, if (lastCreatedAt != null) "createdAt": lastCreatedAt},
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            PostLike postLike = PostLike.fromJson(response);
            if ((postLike.data?.data?.length ?? 0) < 20) {
              hasMoreData.value = false;
            }
            if ((postLike.data?.data ?? []).isNotEmpty) {
              postLikeList.addAll(postLike.data?.data ?? []);
              postLikeList.refresh();
            }
          }
          isLoading.value = false;
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
          isLoading.value = false;

        },
      );
    } catch (error) {
      hasMoreData.value = false;
      isLoading.value = false;

    }
  }
}
