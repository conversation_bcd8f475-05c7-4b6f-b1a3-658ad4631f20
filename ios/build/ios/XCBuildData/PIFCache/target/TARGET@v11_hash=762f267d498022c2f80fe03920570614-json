{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ec4c46dc2ed2e0ff1c51e0618eed46c8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9823f0c7c0f2f04dd6d66bec362b87854f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a68ab0cd90593d738a03b71a3485a50e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bba57b39947da9ccad9cd9e447255a4c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a68ab0cd90593d738a03b71a3485a50e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9877cdb8b2d44ef89d8862e450ebb42b88", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fd364b53604aad9348310dc73fb530f4", "guid": "bfdfe7dc352907fc980b868725387e984d45f4b3011d5d804f29b0a3e377c643", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98be96d31dbebd32ad6cba661aec3b62bc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9805c85a9ef129ea0dae7069ffc1c5b8e3", "guid": "bfdfe7dc352907fc980b868725387e984f343bf812c84a3d3b1acf173c8219c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a48884f8b9cd326f02c5cda4a9525992", "guid": "bfdfe7dc352907fc980b868725387e98f09f58b2762a7d5d545e3b5ec27b2c01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8c097b63e39e2d7388ba0768c2452be", "guid": "bfdfe7dc352907fc980b868725387e98737dc35e4b15d48fd80ad0082a23ac51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980542442824cc5a53e0d4e30918a553c6", "guid": "bfdfe7dc352907fc980b868725387e9877eabdae78e8ebbae9cd3a2868854fae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f8c14ec406d4b8e70d8ed331e8d833b", "guid": "bfdfe7dc352907fc980b868725387e980a3a65e170fe4e750448944675467d5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98685a6c6b98a512b1be096765c5c6597a", "guid": "bfdfe7dc352907fc980b868725387e983c0e99969c47d421eb54382449b414a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805c59c2c6d6ed02bc6cd80481ad1ff36", "guid": "bfdfe7dc352907fc980b868725387e98b5be31805613b50a5e0dbc51ee807d4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f25225f284c3a335c1eca9a7768de515", "guid": "bfdfe7dc352907fc980b868725387e98b3b6d4a96491b1a42c82f7d2ef5b1e00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efeae82e653f0c9bc17928b5b14c9bd1", "guid": "bfdfe7dc352907fc980b868725387e98b1069e868b8eae474b5d0ce7084f7387"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9f95878ab5af38ac1465b3f8d54b20c", "guid": "bfdfe7dc352907fc980b868725387e983a2ba8e3a5d23899d4ff0ddf09855d52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c9352aa5312581b35e8d50128f39ef1", "guid": "bfdfe7dc352907fc980b868725387e9852c503f473fadf940ed35a920a8e03fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0011cc8ee5562da4a91524556392f78", "guid": "bfdfe7dc352907fc980b868725387e98b52fe1b88deaa0cebeb572fca5f13434"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884c9ee52634b92c26129f68a74beb6be", "guid": "bfdfe7dc352907fc980b868725387e9843ea20ba66c04c46afd16ba8b19563e5"}], "guid": "bfdfe7dc352907fc980b868725387e98cbe6c8cbbc80a3de6c6c93d8aa76afa0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e98dcc7bb51754bb7f678e60fe541ce3977"}], "guid": "bfdfe7dc352907fc980b868725387e98c292d42874d18e07867d7ab6d2885319", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98dda556f063e9133483cc3f1c09dd927a", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98b6f5db4f63e427a1576ce8d4f4cd4921", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}