{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dfd0f230093ec8aa9f89d72101fff6c0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/better_player_plus/better_player_plus-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/better_player_plus/better_player_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/better_player_plus/better_player_plus.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "better_player_plus", "PRODUCT_NAME": "better_player_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bce04919b890bd7aed80d2f6fa8850e8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c544f8ab5455c84b07c1d6cde5e127bc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/better_player_plus/better_player_plus-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/better_player_plus/better_player_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/better_player_plus/better_player_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "better_player_plus", "PRODUCT_NAME": "better_player_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a37971b3de621c0e880098ce46b6a95f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c544f8ab5455c84b07c1d6cde5e127bc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/better_player_plus/better_player_plus-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/better_player_plus/better_player_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/better_player_plus/better_player_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "better_player_plus", "PRODUCT_NAME": "better_player_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f292dc7022274121f181fce13cce41f0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d682658522b0e55fe235848a6f7eb1b4", "guid": "bfdfe7dc352907fc980b868725387e98def3817ff757e8824fb55deff783e74d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808381308fc673170803125cf89481486", "guid": "bfdfe7dc352907fc980b868725387e9881529a24dfc52e26a0416fc9a35712be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffef036f5ed88cab23fd93e8002fb2b5", "guid": "bfdfe7dc352907fc980b868725387e983f27fe70c9c22f2066cfe20c84a8a1ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c5dc95d015987047d3ad76a4c142b84", "guid": "bfdfe7dc352907fc980b868725387e98e894f71401ac56a9171860bc04967c5a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884fb617a9f224f05caf0ec73b86f9912", "guid": "bfdfe7dc352907fc980b868725387e9877f947d04d9edf1a7e3a5a912bbe1f16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f344f488ceca1ba43d5f1b478e017e2e", "guid": "bfdfe7dc352907fc980b868725387e9806e027f84015253427751dd741b9877d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982c000c07650eaff32c57e4ed7a779f7d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98603b6b76879c5f3f433933206c1f3dab", "guid": "bfdfe7dc352907fc980b868725387e987d068e393c7db76c74bfb3478f647423"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982790894f019d9c034e8ee00f03ff7275", "guid": "bfdfe7dc352907fc980b868725387e9832a09a927abf017e9f0edec672bf66c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c80cc1a19be925a80cdf796bd61990ec", "guid": "bfdfe7dc352907fc980b868725387e9809d66d90e84cdec488cd4979c7965216"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef0df3d69da267928a9ae439f2623718", "guid": "bfdfe7dc352907fc980b868725387e980021ae63ce963ef0d0de4dc4c0224088"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc0680a4f78449b93f1f4efe1aae38a4", "guid": "bfdfe7dc352907fc980b868725387e98fdccb091c0b553e981b1eb6a06c1494a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98477a50ccedf5e21ebf79b3ec4da33e1e", "guid": "bfdfe7dc352907fc980b868725387e9878a39b4eb3e4e0e2778078c810995d13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c6f5f3babf8dbc9ca4560dd14d201a1", "guid": "bfdfe7dc352907fc980b868725387e98b51907b1406dafde9d74392c8984988f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c9bc4ad122b11b43501550532659d56", "guid": "bfdfe7dc352907fc980b868725387e983955c12db66640d9964b3c8aab7fcc81"}], "guid": "bfdfe7dc352907fc980b868725387e9849c17fa96abf8401a2430c1fe1b392c8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e9807d167788c31b760c3b7dd16df674602"}], "guid": "bfdfe7dc352907fc980b868725387e98a5829fed6fe0b0d418f78ccfbe06fcac", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98025b77a20342d026edf273eff28d1d3d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98687f14f834c716a0a6b72b2f678d5d39", "name": "<PERSON><PERSON>"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98ae4cba9dafde33c1f8a32ad5773d6cd3", "name": "GCDWebServer"}, {"guid": "bfdfe7dc352907fc980b868725387e989f6c9e975471ecf29d91b182619839be", "name": "HLSCachingReverseProxyServer"}, {"guid": "bfdfe7dc352907fc980b868725387e980a765b211b0c8c508dddcb830a52bbab", "name": "PINCache"}], "guid": "bfdfe7dc352907fc980b868725387e986cd267420621a3ff1d813b97fdcfc70c", "name": "better_player_plus", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982979aa52633ff059b6ad20ce50d04f84", "name": "better_player_plus.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}