{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c00363a6ad5f74ada9e2a21be0f28652", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98056edc2bf9640f18388060799b071ec1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d0d4949fd7ab04bacef533ad502cb425", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c3fb1a60de647ecb978608287133cb7e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d0d4949fd7ab04bacef533ad502cb425", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989912dad496fa48d07c5cdb368c75c65e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c355f6c45bdf9fa09c0d417792df42fb", "guid": "bfdfe7dc352907fc980b868725387e98a11767dd59275bfcfd80d968e0bfcbfa", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d46f274682f694a92e98b05a8c25d84d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986200f221ce660a53f05675d9a627b88f", "guid": "bfdfe7dc352907fc980b868725387e9841044b21a9943ff658a768e61994ead8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dfdeec2a58b2828a1ba77845645b704", "guid": "bfdfe7dc352907fc980b868725387e98feefbb459792a5e08cb00fab739ff244"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdfef98e2c58b603fc119953cccec318", "guid": "bfdfe7dc352907fc980b868725387e988ed1e9957304d27122bd0bb801cd52ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986caef7b0f9af2bb445635b56081d45bf", "guid": "bfdfe7dc352907fc980b868725387e981c402ed7d5c0013ef5462bc58382f79f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ab78aa2e34f5da4da98e078432397ad", "guid": "bfdfe7dc352907fc980b868725387e986656357c0e85c1e3570d1303aafe0d27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f416533c0103eb02cdfc2467aa7b221a", "guid": "bfdfe7dc352907fc980b868725387e9837c9945c1107ddcff8d9f02e5289be96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98978950bde92690b09e4c3fb62fb0622e", "guid": "bfdfe7dc352907fc980b868725387e98c02299d5daecec932cc4db266152fbd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980782ee8041d26614172910e61924193d", "guid": "bfdfe7dc352907fc980b868725387e98076e03f54b30e8dc154fdb142abcf3c7"}], "guid": "bfdfe7dc352907fc980b868725387e98c2791cfbea7d0e3cb65df4223c49018f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e9888a74cb35ee7b63bf94b23d2a50157cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c81c87c9ae76013da3d981243a0bf06", "guid": "bfdfe7dc352907fc980b868725387e983015053e7957bb703ecbb0c18896217c"}], "guid": "bfdfe7dc352907fc980b868725387e984c4050d88bf87b87d59794c192cedd4c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f7dbd57ea570a9e39913041a1a6e4ca3", "targetReference": "bfdfe7dc352907fc980b868725387e9865af479ae97320e284a27cf831d212b3"}], "guid": "bfdfe7dc352907fc980b868725387e9845065227e7c233225e97bdf82e2b25cf", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e9865af479ae97320e284a27cf831d212b3", "name": "GTMAppAuth-GTMAppAuth_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}], "guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98855fb84830a2ff40ce73a17fc283f650", "name": "GTMAppAuth.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}