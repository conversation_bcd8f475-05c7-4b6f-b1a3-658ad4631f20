{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c556950da144989e1346c0511104de01", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Cache/Cache-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Cache/Cache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Cache/Cache.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "<PERSON><PERSON>", "PRODUCT_NAME": "<PERSON><PERSON>", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9819c491b8b9c46d93df68f7aecb1d8f12", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9882fb04bab954fd409c74a241e20deeb2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Cache/Cache-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Cache/Cache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Cache/Cache.modulemap", "PRODUCT_MODULE_NAME": "<PERSON><PERSON>", "PRODUCT_NAME": "<PERSON><PERSON>", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9811a596f78049c98a81c550a1166e4b40", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9882fb04bab954fd409c74a241e20deeb2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Cache/Cache-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Cache/Cache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Cache/Cache.modulemap", "PRODUCT_MODULE_NAME": "<PERSON><PERSON>", "PRODUCT_NAME": "<PERSON><PERSON>", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9809662a081fc78a901f8776757b7525cb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e54478245925945e8c0a9d455f8b7e5b", "guid": "bfdfe7dc352907fc980b868725387e98fdf4f6f3cfcf202b7aad698ad22c5b39", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9803e718f716275816f86e5445ddde77c3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e7be9bdc0da399f8eb8e827fbe8063c3", "guid": "bfdfe7dc352907fc980b868725387e9848d1406a7d60baedd7e24312730253eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bc7af38c9101a0b244d5c95b7323d26", "guid": "bfdfe7dc352907fc980b868725387e981e9592da6ffb42a3df577179b093c769"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5ba87b0c195dfb7342d7b2df9fe9ff9", "guid": "bfdfe7dc352907fc980b868725387e98b6c369e8f55aab5a5cf25ffbebae11a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98791346f76511cd77995714a0c3d323e8", "guid": "bfdfe7dc352907fc980b868725387e9825c3e1b8fb91a580b78112fbddcf48ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f11e76b94b60f5ab4d8bbd4e596ae02f", "guid": "bfdfe7dc352907fc980b868725387e98e9964713e58651f69e54a22b1267974f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983af196571de9f97bb507209de475c887", "guid": "bfdfe7dc352907fc980b868725387e98cd56cd05f14a8debf3a4179596277e3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd4452e1e77c8354a15799e2f5fa02bf", "guid": "bfdfe7dc352907fc980b868725387e985db1d429593238942a0a8cbf9d8a7450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878c56788b4d500bd705cb9eebe81bd47", "guid": "bfdfe7dc352907fc980b868725387e98d5400cbcc9e7fd01bbe07a040ad8f238"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98843219ef976a2ddf6bca436202459e2e", "guid": "bfdfe7dc352907fc980b868725387e9821d4bc4a974e61544fa603b54c089a75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c03566f0e0d40a93e5a025cf7984bcdc", "guid": "bfdfe7dc352907fc980b868725387e988f7a63334447305bd9d740496c877c49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98388434d0c28e8a4b34cd52d25326c6f3", "guid": "bfdfe7dc352907fc980b868725387e989ff09a3d06875ff51d47f52155deb6af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833e2a8abfe422442dbd0e0dcaa8e5df7", "guid": "bfdfe7dc352907fc980b868725387e9842294fe7997ab25594f86f6aa8cbc840"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879d85862696eecf053757a3cbf7503f5", "guid": "bfdfe7dc352907fc980b868725387e98af97ed3e8fdbe59d796f5c9360ba99a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d93fe5ac2b0ebc5592798cb653a00e91", "guid": "bfdfe7dc352907fc980b868725387e98ae14b2f88e9d6e21e1679180decc6bd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98375275aca51682a86c0c32b73d002a8d", "guid": "bfdfe7dc352907fc980b868725387e98c61e280448ae53e7eadd7a89a60812fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faca51538e31e99044fd5af7408c2576", "guid": "bfdfe7dc352907fc980b868725387e98f364057cd1b6bf0a2eb34f7d6013b3c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6c409120b913da6c2ed2bfaac815556", "guid": "bfdfe7dc352907fc980b868725387e984f4d71dbccccf8d11130b579b75bf89f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcac8d0866ca11347e3565676690341d", "guid": "bfdfe7dc352907fc980b868725387e98138b8b6ed27930f5f488aa6779272b01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980714fba5f415655fd761651f17e464cd", "guid": "bfdfe7dc352907fc980b868725387e9845ddc1b6d7f967964024aa9fe4efc2da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc2509813fbfc5f0d0940d2540208e60", "guid": "bfdfe7dc352907fc980b868725387e98de2ba9b84148be7141bb86c2813e417a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7b5abc22c8dbad247d9136d75e47e9", "guid": "bfdfe7dc352907fc980b868725387e98ae294f3e9112714fa336d4bd9e5e8d2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aadee9d822a7518a9a28e658e1ddf44a", "guid": "bfdfe7dc352907fc980b868725387e98f3464d1b96a27489303cdfd1e5ca3824"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc83a62bb4ac7c5dfafbb79ad1e09773", "guid": "bfdfe7dc352907fc980b868725387e98845738e1a1702edfa6186be0207d4386"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd53e047b1e82fd410fbf0ac4615b8b0", "guid": "bfdfe7dc352907fc980b868725387e98963954c4d78a40da247d54205ddb4254"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98468ee7a5501de989dd8e59039cd19143", "guid": "bfdfe7dc352907fc980b868725387e98f3a1924150a56961401d9b4d65b9a8b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed6cd448b4a41c972ba9bb1d3e6e663d", "guid": "bfdfe7dc352907fc980b868725387e98eee59649227b77af8222a536175b2df6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9eb40b08fbddb1db87a2829b6633c2c", "guid": "bfdfe7dc352907fc980b868725387e98dba51e1ee85977e1ff5261b38d5cd31f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847e6dcd0833b1ad4db4e1fd40e5dea31", "guid": "bfdfe7dc352907fc980b868725387e9863729b96885c46e7ea3d56515f6496a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98803c552a20863b7f2ce733d9ef930db6", "guid": "bfdfe7dc352907fc980b868725387e98c31506c5c2dda7be099d37185af1c519"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846e4184792529ad90f85d1c7c0c7dcb9", "guid": "bfdfe7dc352907fc980b868725387e986acbea0c6f997b01eb654df526e22167"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2449ba72b5d77b558e0a6a9a57b1b34", "guid": "bfdfe7dc352907fc980b868725387e98dcd65ca4fe88590160b5f52308b0e6a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac8806aa5fba9029879c4382de4572c7", "guid": "bfdfe7dc352907fc980b868725387e98641528d2d9f4cffef700013170d4e529"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a16f3a576e4df2c409adefa549b69a0", "guid": "bfdfe7dc352907fc980b868725387e9899125d5b2f7b83a7ed38a0c6ba7cce26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894267d7fcf9d04d8509829ad08b1343f", "guid": "bfdfe7dc352907fc980b868725387e98a91d1190938238727483a444f3816d7e"}], "guid": "bfdfe7dc352907fc980b868725387e983a0aa2a1f5d801f8c09ee3559e1daed5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e986c38e0252e037583c5b90ea60873512d"}], "guid": "bfdfe7dc352907fc980b868725387e9805391d61e995fa68e1f2d97a3734eef9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98f88e3121499c86961ced27909ba01cd6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98687f14f834c716a0a6b72b2f678d5d39", "name": "<PERSON><PERSON>", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98fdbf8692076b1f163449c92d81d4a201", "name": "Cache.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}