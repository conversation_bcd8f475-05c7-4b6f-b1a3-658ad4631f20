{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a720a32066944334bee3d2295c5f245", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98038206d6708c2318e327951fdd942c62", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd5e273090b4c68db54afed9bd14d0c0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983cbf5539def16406a7880e1781a05ca9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd5e273090b4c68db54afed9bd14d0c0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98df3fad6f442c392f003be1b32eea74d9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d06feeb602fa0bea74c0253d0a8e5bd", "guid": "bfdfe7dc352907fc980b868725387e987fa7f13ca9281a8b106796180b6d6df5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a39cee6980a1c53d4f00190292c56bd", "guid": "bfdfe7dc352907fc980b868725387e98ed58a8c541bdc9fe36560e9846cb8dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897828afb0b7c251014dca002e13ebf73", "guid": "bfdfe7dc352907fc980b868725387e986806d8210b38593c2aed68197b2206a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d83a15c70f05120ab71cb99ba41c47a", "guid": "bfdfe7dc352907fc980b868725387e98dea48d239938db01d547f56ff6ec97cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6260c4c43e37be530d0c29ff48f87c9", "guid": "bfdfe7dc352907fc980b868725387e9882504a3b9d34fb13a20f4de065f92910"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98753a8e1882510963ccf6e9acffeae57f", "guid": "bfdfe7dc352907fc980b868725387e9880639aa09f0c9e81f0e2d70cd7e65076"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819c0a46087841311dd72652616cb54ce", "guid": "bfdfe7dc352907fc980b868725387e98c643c98cd95dae06b9cc07f4437c1909", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898ed0693e3015a874658068605b152d0", "guid": "bfdfe7dc352907fc980b868725387e98ae2282a1d6309ce7fb2947f7bde149d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f86b6b3d5fc90492943ce55cfbd5903", "guid": "bfdfe7dc352907fc980b868725387e98b33abf84ee0ef6b1f4ba171c446b4195"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd2b92baf8750980924ef17ad6e5cd36", "guid": "bfdfe7dc352907fc980b868725387e98ec7a926efe946ee6ec8a805bc13aee6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804ef545cf0f56355b127a174bc486227", "guid": "bfdfe7dc352907fc980b868725387e98dc3ade88223255a0dcc9aca497a2b77e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e8cd798c1c0ec3e74ce677d0b991f33", "guid": "bfdfe7dc352907fc980b868725387e98a2a0a990cfd46930041d5d75f1d91965"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892672fa814466e8610cc6458b9ec09d4", "guid": "bfdfe7dc352907fc980b868725387e9891d0fe9ecfa0bb1a4e029658d20c29f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4de4bfe01cd2d3341834165385b6229", "guid": "bfdfe7dc352907fc980b868725387e9868c37bd4608673b04bc8d4c767e181e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b19f40acc1a8d61fe822b83ae8232ddb", "guid": "bfdfe7dc352907fc980b868725387e981a3ab1f6ca6081fea5534dfe034487e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c56c9995072957bc5a88341561acd694", "guid": "bfdfe7dc352907fc980b868725387e98399c9a1c74b5d29aab34be9ae8ace4b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f37a86e5dad6d3be402138e5a9fe444", "guid": "bfdfe7dc352907fc980b868725387e98e8730b68c5d3b5954e8829afe3d46aa7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d29bd6eb6b1f76770e6521c6425f32a5", "guid": "bfdfe7dc352907fc980b868725387e98e66b4f86ec6a2b793f3d42f8d2db6144"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831cbf5d970b54a7e74b16f3269d744c4", "guid": "bfdfe7dc352907fc980b868725387e9832290d7ef92b123c28c65706320f84a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98740fcec9b1f7339875e0db247b0fcf6e", "guid": "bfdfe7dc352907fc980b868725387e98439d5d08f61068a331d3fadade534f55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f48ad9f14182b59db654c3cb04b2b884", "guid": "bfdfe7dc352907fc980b868725387e98c488c8420594159402ef60bd08859ac6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a3f46b653c71e3adf52cbe874c44e23", "guid": "bfdfe7dc352907fc980b868725387e983d47fda3d91a11292e9a8c2675a37be1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98848414925b67b466316587bf8c99dab2", "guid": "bfdfe7dc352907fc980b868725387e9833554a78201766330e8df15624ed02b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fed02a3545acfed8fc6f8c648c15d675", "guid": "bfdfe7dc352907fc980b868725387e98c9d3e2ddfa2fdf4a43470f5a8013d163"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0714685771372c9536419c9e0a2c439", "guid": "bfdfe7dc352907fc980b868725387e980c4a8a779b8406b2e65467becc460da5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825bc0c1a2b29a52b43c04b567c636cde", "guid": "bfdfe7dc352907fc980b868725387e980932cc9f45f529b08ff39decb1b719e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985432e2212d18aaf200b3b7b98ce5c4db", "guid": "bfdfe7dc352907fc980b868725387e98c6816b458932be883f246018c30d8b76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f844f2f6ff9cad44428c86595ed0644", "guid": "bfdfe7dc352907fc980b868725387e983b8e9075e4de149a95f2432c514b4f94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2a9c7d7a824624d0802de2e5d162217", "guid": "bfdfe7dc352907fc980b868725387e98e058162f268bd6ce3fdfa2c9f36d8bf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef4d5ca5c932c2a00d4750378759abc", "guid": "bfdfe7dc352907fc980b868725387e98d0ada44f60e1a3c916b7931b91af7d0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b77488245a3aa9e4005b08a324b6a68", "guid": "bfdfe7dc352907fc980b868725387e983553752849960ea4f4f379e9b6259909"}], "guid": "bfdfe7dc352907fc980b868725387e982a9947b2d3622a2dc2ddc588b155e80d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987363030646a4bc2f52db1e251f36dbf5", "guid": "bfdfe7dc352907fc980b868725387e9890cf9d9797649333a4bf8a9eff82f7d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981673f0feb11c2236c3a108442d4067ba", "guid": "bfdfe7dc352907fc980b868725387e980ec04db0c6c6a16bf9f87c3e5d3604a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3bc9bab5cdbb1c4d677a1b1bd5b4883", "guid": "bfdfe7dc352907fc980b868725387e98aeff2067180fd9de8f5b5ebd0dd09261"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eafcc6ced313140bbe4512a983c2f2b5", "guid": "bfdfe7dc352907fc980b868725387e9848339a3ac32ff1dd92b3f62636beb461"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f2c27e87b26ad8e6e828435fa558f07", "guid": "bfdfe7dc352907fc980b868725387e9821c1ff966d2fec0d0d09b348038571bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ad5806c9defeeca4b86788787b68550", "guid": "bfdfe7dc352907fc980b868725387e98fed8314f6fc2047040312ca0fa3286eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bfe42fddc0de69c06a636cf1635dd79", "guid": "bfdfe7dc352907fc980b868725387e984728a5ad3522dd8fd988d678523a398e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867fb9292ad5a99305b47c3c87733e7a0", "guid": "bfdfe7dc352907fc980b868725387e98b107371149cd1a32c0b2b9f4a12a06f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a591edabb37b39aafabc166bcef6540", "guid": "bfdfe7dc352907fc980b868725387e98d1e79154bd5ba4c73c0227e9788cb5c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3a18bbf4c8a58330e7c7b214c622c32", "guid": "bfdfe7dc352907fc980b868725387e98633ff0c57c8e50afffb2a2eb89cec099"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956aa363441bfa2e4f644136ce875efa", "guid": "bfdfe7dc352907fc980b868725387e987d406d61d6d40b2ee2451ffa4410c12f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989239ed1bb1f82344ae21969c5a4f2b12", "guid": "bfdfe7dc352907fc980b868725387e98718c66b81bb42d280bc5daaa084f6c7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833c1ae05b95d60d697b3e7cf5daee1b0", "guid": "bfdfe7dc352907fc980b868725387e985ccc1090db521a780cc12e9207dbb565"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98860b92a679edeba949cde5cf03c15832", "guid": "bfdfe7dc352907fc980b868725387e98efb16d9d449b706740aa331a7138d81b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985344d09fb8be1dba4658bca15e4040a8", "guid": "bfdfe7dc352907fc980b868725387e98df42ad77e4acaedffe6585deac68a43c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812cf1686fa95fa86b5096474411c310d", "guid": "bfdfe7dc352907fc980b868725387e98e8a92a495d119a18d87bd9069acc3d72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98887a285aec9d0939de875215bdb9735d", "guid": "bfdfe7dc352907fc980b868725387e98cb1385eddd002f21c8e5436bb815a6db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa2ea9b4fca08ace0d00ad6082b3d756", "guid": "bfdfe7dc352907fc980b868725387e984f92a97b4fd1bfaaadc621b9e860e3ee"}], "guid": "bfdfe7dc352907fc980b868725387e9889a669a6a37a7dcad8830a1da4a26579", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e988f9019df41816216258f4877a695671b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c81c87c9ae76013da3d981243a0bf06", "guid": "bfdfe7dc352907fc980b868725387e98969ddbd54481459fd18ec8f6d1ffd797"}], "guid": "bfdfe7dc352907fc980b868725387e98f0e81725f8ee75e980c07f8e0902bf1b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987cb5160f4075e8a487b0803d28cddd18", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98110152b1b57a5f51a2eb2ed7c2811179", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}