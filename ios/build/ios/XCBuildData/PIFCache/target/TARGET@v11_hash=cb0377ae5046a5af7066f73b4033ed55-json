{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4a213df8f0a66870da711b10e228184", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PINOperation/PINOperation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PINOperation/PINOperation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINOperation/PINOperation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PINOperation", "PRODUCT_NAME": "PINOperation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98de93205d17e27040ed986775d642aaf2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983d45b5c7d00e746f7b1f6d37c29fe736", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PINOperation/PINOperation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PINOperation/PINOperation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINOperation/PINOperation.modulemap", "PRODUCT_MODULE_NAME": "PINOperation", "PRODUCT_NAME": "PINOperation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98592412b0bd14cf93eca1361be29ef51c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983d45b5c7d00e746f7b1f6d37c29fe736", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PINOperation/PINOperation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PINOperation/PINOperation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINOperation/PINOperation.modulemap", "PRODUCT_MODULE_NAME": "PINOperation", "PRODUCT_NAME": "PINOperation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981b9838010697ef6f1b2ba1b847d7f453", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ae0b4b3f02a78247e0e97725a5caf4cd", "guid": "bfdfe7dc352907fc980b868725387e98441aae10b6754b697cfd1f425d8c5e03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adb9c61f4195e74eaee219951dda5f5b", "guid": "bfdfe7dc352907fc980b868725387e985b26ee160647c723995cb9652efbf73f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807bedc41eed67b8fc15fa71966851318", "guid": "bfdfe7dc352907fc980b868725387e9877a2ad51f6133d0be549a55d04483d77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ee154b908cc55c80ff120556c36a5f1", "guid": "bfdfe7dc352907fc980b868725387e98078e972efde9317aa704125eb9b001dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bf57d80c84159b7902c179e17ccaf7c", "guid": "bfdfe7dc352907fc980b868725387e98534ecd05ab502bdc4829ce800e65bded", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98828941bbbac19f10ea39fe7f0792c0d7", "guid": "bfdfe7dc352907fc980b868725387e98c8c564bfa3c6f01d803c8aac3e5bd2bf", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c48a856a7a0b1a406e8c21260cc1d1cd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9832a4422f693a7c6533595781e19b0f58", "guid": "bfdfe7dc352907fc980b868725387e98b41acd299c64a76ae0acf0febeb0d5eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b770675479ad4eab18b28c927dbb9f92", "guid": "bfdfe7dc352907fc980b868725387e98e8828bf9186266e8f40088b3a49f3d0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e47e46011ead66bb6f7afc34c16d5f2", "guid": "bfdfe7dc352907fc980b868725387e9823ac7e7bd6f780cae8bf51599888bf01"}], "guid": "bfdfe7dc352907fc980b868725387e98e8386a694644b637a37efa2312fd9079", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e98fa249356cb690c8743d26a1e212051f1"}], "guid": "bfdfe7dc352907fc980b868725387e9849ad3943868263e1c9cde515c4b74fab", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fead8f8097cf9538c9e58dfdcf127b2d", "targetReference": "bfdfe7dc352907fc980b868725387e9872a1d0c9b4d65372f854ad7ceb04716f"}], "guid": "bfdfe7dc352907fc980b868725387e988a9ccf6630828b97475f8b96c4303d69", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9872a1d0c9b4d65372f854ad7ceb04716f", "name": "PINOperation-PINOperation"}], "guid": "bfdfe7dc352907fc980b868725387e98d09cfc976f4622a7180a4de1a3b0a290", "name": "PINOperation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ccd5063b353607b0bc283d743114ffaf", "name": "PINOperation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}