{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987466e2c77c71da76f76eef83106f09da", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fc18b27f04237553e74b72ef1ee69d72", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9817c497070f58415591d16c83cbe89d9e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9877d3f13f0f057a81634f4ff54084bbf2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9817c497070f58415591d16c83cbe89d9e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98947efcfffd05ed3e66e41ab2c2f573d6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d43950616489f9bc1f166d549b4886d9", "guid": "bfdfe7dc352907fc980b868725387e98f88053830234e71df1735835dd5a2e18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eafb0fd6a9c58bf37bd7a6499a9d775", "guid": "bfdfe7dc352907fc980b868725387e983824b6b41253568e53ebf4479b96276e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e57051599881a2b3e5e049f03adfe91", "guid": "bfdfe7dc352907fc980b868725387e984396cf44c17f8b6ac8a7605cf9661692"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f806e332be63bda9ab8d23335191bd9", "guid": "bfdfe7dc352907fc980b868725387e981107d59a21a13110012cfd92cbc3f12f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e928b72a38d1de555633bccc14c5e9c5", "guid": "bfdfe7dc352907fc980b868725387e980a25a1685593407f52a4efea149bd938"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98082f77add5539d8e2de8844d85bc78ec", "guid": "bfdfe7dc352907fc980b868725387e983b219188087f82a55e3f4e032fd8218e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8be7b34cd5281744d4326ca32ef83c9", "guid": "bfdfe7dc352907fc980b868725387e981dc3b9d6d6a545f687c0d99e167c2f9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1f565e34401f789b2622299d6e904a4", "guid": "bfdfe7dc352907fc980b868725387e986f31ee5102b9b3f04961adc34297d000"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885d02e36640754e46efe840851a6383e", "guid": "bfdfe7dc352907fc980b868725387e98f001018fc1736a6e5e58e3bf5db8542b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e64a32ee2c7c0a28411ca90d2bb4dce5", "guid": "bfdfe7dc352907fc980b868725387e9896df82fe93f27d0fd0fba119eb14fb4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eca41a19ab3254c9e50bfb35e9d9c34", "guid": "bfdfe7dc352907fc980b868725387e98a5e0864dff84f4e7c224e8197bd75d75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f52d579d5e7b57cf1497c194b611835", "guid": "bfdfe7dc352907fc980b868725387e98b457e1c35e27b959b0577e8b7da32630", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e61f44510df5eea7c30aebbb48567c6", "guid": "bfdfe7dc352907fc980b868725387e985d874b4edb0878982dc986c34934fdc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f0fb1cd3bd6ac0ce4918998b0ad992d", "guid": "bfdfe7dc352907fc980b868725387e98c2655bfdc4174f988796d5ee46abf3e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ed07b9c64870439687f6d169df1bf98", "guid": "bfdfe7dc352907fc980b868725387e98445d3d2b52385c2f063850d8a5f8833a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ad3deb1317fc29e8376cb9e88ab7f60", "guid": "bfdfe7dc352907fc980b868725387e98a241393a43917a5d96a72724f24ebe31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc6cdd6fc48c07b1a1cbd65d0b74808", "guid": "bfdfe7dc352907fc980b868725387e982530391689279f780b5f8979191f10a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98579485ce40bbef7ffb8baf1f6043cd04", "guid": "bfdfe7dc352907fc980b868725387e98a682b2e505fbff52fb2a13f061b17941", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fe93dc7cca3730253bb12dcb128edac", "guid": "bfdfe7dc352907fc980b868725387e9831a5ed716fe4ce3c3045e4d073b3a234", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843f1cac109156a853a183b56b8cfef4f", "guid": "bfdfe7dc352907fc980b868725387e98685a7e9d2e2eb748d0f604c20e7a87d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa836281d2d4ca9f48bda7896053768a", "guid": "bfdfe7dc352907fc980b868725387e98461561c595cf9dccb480dea5a3a99663", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc2405471de4d41ee5717befed0c66db", "guid": "bfdfe7dc352907fc980b868725387e9872fd2e691eb913ac3578b207df94255d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880038a5983a9bdf779a6d296c99f3ef9", "guid": "bfdfe7dc352907fc980b868725387e9825018d0b93c0dff7405b6ea629090ac5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f54d28d207b1cb3239c0ab46d153bcc6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9846186ac055d24605f7f8c8f8210e5226", "guid": "bfdfe7dc352907fc980b868725387e98e5db29a8ce76d18754dacf2408b37f10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988976dbaf1a1cadea422ecf2029f604c1", "guid": "bfdfe7dc352907fc980b868725387e987b46c004f188f175753a32953ae9b563"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987182c0a51de69f6ea435f122e0d3d523", "guid": "bfdfe7dc352907fc980b868725387e985cb72c234bea74f95d0fe4f38aa6c88c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875d8eec174add7486451ee4b2be278b1", "guid": "bfdfe7dc352907fc980b868725387e98542940520f6087d9cf2c12ac373d2251"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b231995f876502654a46b9a08a81f89", "guid": "bfdfe7dc352907fc980b868725387e98f7273861290e308155c5722c7ebf3110"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989012c4072c82ebf5938840c8930a1a82", "guid": "bfdfe7dc352907fc980b868725387e98732b3e9e22effae2b36f414cc40b15f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815823394de7c55e7fc8e8d88a887f504", "guid": "bfdfe7dc352907fc980b868725387e9824f914cf494331787eda788f589f2d20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98171f84d58889645009771abf721d13ee", "guid": "bfdfe7dc352907fc980b868725387e98fe0bcfbff30ea85dfe0b8d2df98adada"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98066d9cce57387511748116497cb4a5fd", "guid": "bfdfe7dc352907fc980b868725387e983166e9b1101dd4166a618f3c1baa6e2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be444727d01bb24acdc8047e043e5da4", "guid": "bfdfe7dc352907fc980b868725387e98c4c9c119d540aceec5e89d2f840e7dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be79a3c16db7c1f41887237b4a02785b", "guid": "bfdfe7dc352907fc980b868725387e9818b689c3e47b03e5bd97b76794519f05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980745b9a4df66faec6acd8b62d732db04", "guid": "bfdfe7dc352907fc980b868725387e981539c47b38d8bf138b630f5f9ec04888"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98116dce3705ddd547154f5854a2e374c1", "guid": "bfdfe7dc352907fc980b868725387e983bf296990a32012c9788becefc29cfb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc35d06208b44fb229c95bb3f8fdaf9b", "guid": "bfdfe7dc352907fc980b868725387e988976b655375bcb257f2cf73a01931d8d"}], "guid": "bfdfe7dc352907fc980b868725387e98af9c285360eb29eddfd4c91074c6b005", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e982c991ea37c5e6a529109dc184aef3252"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea565b399103566eee5262dbfd427ed0", "guid": "bfdfe7dc352907fc980b868725387e98b7abecfdf9306677ff55b95b01703fc9"}], "guid": "bfdfe7dc352907fc980b868725387e985ad75faad5be34d3d1824377e7797a11", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a4179cf5a2e4bf95f6fb7225f3f10eb8", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e982c94ba402951b7503fe75e94a6d58a2f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}