{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f197185dd86356474df689e868b713e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PINCache/PINCache-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PINCache/PINCache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINCache/PINCache.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PINCache", "PRODUCT_NAME": "PINCache", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989756c9ac7e80606083bb57c37786b406", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd43beabe08ccf5729f33ed02123c987", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PINCache/PINCache-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PINCache/PINCache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINCache/PINCache.modulemap", "PRODUCT_MODULE_NAME": "PINCache", "PRODUCT_NAME": "PINCache", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98927592641505e609f5309cae86760ce5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd43beabe08ccf5729f33ed02123c987", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PINCache/PINCache-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PINCache/PINCache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINCache/PINCache.modulemap", "PRODUCT_MODULE_NAME": "PINCache", "PRODUCT_NAME": "PINCache", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98deba9fa7770a99316b7c126cbf17e244", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98528719ac0f6c99cb0e1225602cb1d38d", "guid": "bfdfe7dc352907fc980b868725387e98b34eda15b3d160db3497c9039ba9e68a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880710b406e8ec5330a4c89b930e463ec", "guid": "bfdfe7dc352907fc980b868725387e98d0908fb9b1cb7a8365aa9998b6dbafef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98125856538a346b3e79318d9c38feb540", "guid": "bfdfe7dc352907fc980b868725387e9815d38454137f5bdcc565f2f5f4ae6601", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855d7406b75ef865b187f0a2ad3cd8846", "guid": "bfdfe7dc352907fc980b868725387e98c2cfb83a1622a36a2d2b7faec2f96741", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b6dcc4f11116b6932fcc7b7ad80ebf6", "guid": "bfdfe7dc352907fc980b868725387e986db6b7c0b5429b95ac1f802afe95f880", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bc28e6b487be6e25d8ed9bf2184442c", "guid": "bfdfe7dc352907fc980b868725387e98655acd305aba892797fa84ae59df7736", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98208cb94afa90c884a94ecf46a6fc688b", "guid": "bfdfe7dc352907fc980b868725387e985b88575b869f57b6b678ca511c4cdc88", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a6893a93c563c2ad2f1edcffdff825c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cf941215be7b3db8d3c4ba481ad6ab37", "guid": "bfdfe7dc352907fc980b868725387e982c2087c8221922025492f067ec29909d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837e8d5365a6eae1e1cf952a2b0f3b09c", "guid": "bfdfe7dc352907fc980b868725387e98489405f16d2eb023c359d2546a9f9840"}, {"additionalCompilerOptions": "-fobjc-arc-exceptions", "fileReference": "bfdfe7dc352907fc980b868725387e9809f069e8d812964190e70d4abf9eef65", "guid": "bfdfe7dc352907fc980b868725387e98219c604dc877417eb4183bf2a9cf6c65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98373d78ab2fe36e78162786c833077f83", "guid": "bfdfe7dc352907fc980b868725387e98ac737fe3258c1d0107c1d29e1a6d150e"}], "guid": "bfdfe7dc352907fc980b868725387e984a54e1d56153750c827d82eea60e1718", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e98af367c3484c8672fe4206f8a727afae7"}], "guid": "bfdfe7dc352907fc980b868725387e9890c9db855884fabf3fa397b6eec0bed7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9836dfda963dd8a1424959ec002dfe124c", "targetReference": "bfdfe7dc352907fc980b868725387e98956b709ce41abfabe34269a056c97973"}], "guid": "bfdfe7dc352907fc980b868725387e98c496a5b3c6940a4dc58ca423caa41941", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98956b709ce41abfabe34269a056c97973", "name": "PINCache-PINCache"}, {"guid": "bfdfe7dc352907fc980b868725387e98d09cfc976f4622a7180a4de1a3b0a290", "name": "PINOperation"}], "guid": "bfdfe7dc352907fc980b868725387e980a765b211b0c8c508dddcb830a52bbab", "name": "PINCache", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98329516f5a905f2fb033999940b6a62f1", "name": "PINCache.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}