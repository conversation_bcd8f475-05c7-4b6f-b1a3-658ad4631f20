{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984c34fd7741a76dc0776efa528215620c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398bac938fb47b84c16f19633c15c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986ee1db5abaae5db7820bc3a0f55c83f3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839d79a52f24f54321de12379511903ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986ee1db5abaae5db7820bc3a0f55c83f3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894fef8acb7de742f96aaab49fd64da62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ccdc5dc02a2c73e34c54832adc466ba7", "guid": "bfdfe7dc352907fc980b868725387e98dd2b3863c5d15e93e7c5e88a896aefa1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a3082a2cd40666369c4e32ce4332b88", "guid": "bfdfe7dc352907fc980b868725387e987cb9e8b5abd65af173bb488380c96261", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98307dcda9f09992383e5cee0d2cedcf59", "guid": "bfdfe7dc352907fc980b868725387e982b1b9310b853c9d96a1d5b4a1e72b68d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b82c173967cba113e00b22f89c2a4389", "guid": "bfdfe7dc352907fc980b868725387e98f6b72aa5e570dfa8ee6058a7df3eda77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983afac7f282e4d603e918e099d3b2409c", "guid": "bfdfe7dc352907fc980b868725387e9854d2145d07da2e08b0a1df7b08acf002", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aa0e5fbb6bf6498e0deb798fc56a197", "guid": "bfdfe7dc352907fc980b868725387e984276f5741a37e5d035d42a235e6ad705", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d234123d238492db6a551310e06a4f84", "guid": "bfdfe7dc352907fc980b868725387e980642a548123fbfa1eb89a9fe2c311d35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c56282bb82663698e249df7ef1f631b7", "guid": "bfdfe7dc352907fc980b868725387e98de912f230bb0c142eee21bdd7bbecff8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dbf4f215dc54075787c00b76549da15", "guid": "bfdfe7dc352907fc980b868725387e9847057d6ee2db89872da5843bdb4dc996", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc92f751d64e4dbc7bec9ed4a3e76881", "guid": "bfdfe7dc352907fc980b868725387e98f5e683072cb2f2ccd7cc8dad0ee3dc53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e06c05141942d8a5031cfc3b9c738a1", "guid": "bfdfe7dc352907fc980b868725387e989f6fdbdb3ec8a4c13de3be35ebd3a5b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aae38ce4b15525afd88bcf80bd9881a7", "guid": "bfdfe7dc352907fc980b868725387e9897df0186bed821e10eceab57ec51bf0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b94bfe6c24a60b5cefaa0193bf6d1a3", "guid": "bfdfe7dc352907fc980b868725387e980e966315eec3f80916ca8ca3c5bee4ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ef4afc8679b6e7a0e93e37d07aa0397", "guid": "bfdfe7dc352907fc980b868725387e9885b0ff48f4e24c58e4238e6f8f3b38c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98024f5a3a2658adbeb8617884117a1156", "guid": "bfdfe7dc352907fc980b868725387e989314e081c3f5bfab1279e3c2c1139a24", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983145ad27ab9a5d703e29269b9b2b583a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d8f613fb32772883efc0827fb68cfffc", "guid": "bfdfe7dc352907fc980b868725387e982bd829b14a0ccbe16ad793a4119d2cd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a52086f657d6304a9a7e7718866d3c77", "guid": "bfdfe7dc352907fc980b868725387e98af9660ddc2107a9cd410d6c3ed7be15f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea456e93b6f31960800d32a5201cd5b1", "guid": "bfdfe7dc352907fc980b868725387e98b20853401f59963e0a4d3e87e87fda01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ec70d76d1cf33aee7bbe5f7e939360c", "guid": "bfdfe7dc352907fc980b868725387e98de533744753a7ee373399d119e5e62fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfc9efbc70772ec8974063a43fb7c35d", "guid": "bfdfe7dc352907fc980b868725387e98ce1d715dedbb66dfad0f4a1f232cde2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98000024746198164a660899668acc75d1", "guid": "bfdfe7dc352907fc980b868725387e98b9c85fc42cd52bd6aa0aa90c356a052b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98078eb4eb2415d49467f1655c0d7c6c8d", "guid": "bfdfe7dc352907fc980b868725387e98c93540ae74f80402c0ab2b6880c64bc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c23967157c82541f9c26e4e7103d39dd", "guid": "bfdfe7dc352907fc980b868725387e9869d59e5f6a2852cb69ab4b97d4ec36ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a70717a71cf7ea13ea174bd75c1bf6f3", "guid": "bfdfe7dc352907fc980b868725387e98c941ce29050e90d0d1202b7f5aefd176"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4745609feff610fa85c9476dc511fa3", "guid": "bfdfe7dc352907fc980b868725387e98c96a14345cd4a18708b9d6511a08430b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984403c8f57923432b1678c09d0c860132", "guid": "bfdfe7dc352907fc980b868725387e98a89106363ecf1c82c77a5e97d941ae89"}], "guid": "bfdfe7dc352907fc980b868725387e9884539da3bdf2ed4ad426ca202aaf6bc7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e98c1dae7e29df157eddda815ae5462108c"}], "guid": "bfdfe7dc352907fc980b868725387e98119e723bd2257d68f10fcea70feb1342", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c10b7a3ff26395a834064a0172d6124", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98a3435bfc3b1be550d78f0653eca9c848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}