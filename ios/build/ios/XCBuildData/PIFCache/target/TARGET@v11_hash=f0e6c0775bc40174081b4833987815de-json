{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988d22afa6b801fa5509bd7e7c66ae6b76", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe6008bd0ea7d60a239f5cfb496f9fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98faf2174f2267c12c688e65f4a1917abf", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ac27f284fbf5fa4242b11e4c252631e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98faf2174f2267c12c688e65f4a1917abf", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec29c327167a123c1352766ac15557e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986e45ce1fcdc5ab0acc8aeb9200872c7b", "guid": "bfdfe7dc352907fc980b868725387e9817011278146d850ad8c1800732eb1e38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f965d3d194772c74599eb1e4969d8d8f", "guid": "bfdfe7dc352907fc980b868725387e980ac0e33a793cc19ff4abfb357eae768f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817d4d860706aeecc234c24ff60586a6f", "guid": "bfdfe7dc352907fc980b868725387e989d6f395bb26cbecbfd60c1405ceff351", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a9eec43784d257feaadf4232faf52fc", "guid": "bfdfe7dc352907fc980b868725387e98df995503faf86a7e536e8fd0813b9b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de96d9ae62ca3068c93d4513d81643fb", "guid": "bfdfe7dc352907fc980b868725387e984a42ac0fe50661fb4253f6986ea45bd3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3189c66fdfe447ad6ee99b846450260", "guid": "bfdfe7dc352907fc980b868725387e98de0db65b473b31d0081f4a5269702284", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f04fbf32c0900de3c0f5444312eef08", "guid": "bfdfe7dc352907fc980b868725387e9860e7129d76385a521f0b9fd136393b10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fe6be1480f7f97c590678c2e3a06339", "guid": "bfdfe7dc352907fc980b868725387e98c102fd841861eee3b0b8bacef085b917", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e6cf1952d9e047988a35b05b0432034", "guid": "bfdfe7dc352907fc980b868725387e98f8633a22aa08547c812ac9bec8b70ea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98458caa570fc6b6725e22ba3b1d187b92", "guid": "bfdfe7dc352907fc980b868725387e98daf47c85e530f3405587d1d40166baa5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ff4591215fe20cae8f12163f774dfe9", "guid": "bfdfe7dc352907fc980b868725387e98acadf1494a3e3cd73f3caf0731d75f80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb6592098c6e71b15f51c9368fdfeee4", "guid": "bfdfe7dc352907fc980b868725387e98cdf6a5c0e94262f9f4ac605bffef3bfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe26159f8887611fa29e4f45a7cc55f8", "guid": "bfdfe7dc352907fc980b868725387e98749c0e50cc6ab0c5d2ae99062d78bd10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98555e0bd63ddcddde671781320d6614be", "guid": "bfdfe7dc352907fc980b868725387e98b60c9e9460d623e196c3542325e313b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bc8a885c96561c4093c9c223928b0d5", "guid": "bfdfe7dc352907fc980b868725387e984176d1afb1f259dfdc07efe17ea652cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df2fe8017999e019c09fd3c0b925021e", "guid": "bfdfe7dc352907fc980b868725387e98bc8f2f12d75589e115d9449c5986f9fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9054f78e6012ca78f54f4426df79dcd", "guid": "bfdfe7dc352907fc980b868725387e9818b5f752d3526a5d027411efb16100bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981142bb8d0dec5b5f0e1f6377e60c1a52", "guid": "bfdfe7dc352907fc980b868725387e989de49e9d16c76d6cdb2cd05defe828b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98952eeed4b293e0033c260c9aa07023fd", "guid": "bfdfe7dc352907fc980b868725387e980869c8041a3db4283bedf358c81f931e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803b85f04e580c6b16c71f2f77addb737", "guid": "bfdfe7dc352907fc980b868725387e98c1f1e163902f92d3f444c8eef23fe9e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3b8016c087eebba5a6bd11cf9447387", "guid": "bfdfe7dc352907fc980b868725387e98ae14d59b03d674cb5e1bad0f3d076dd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d84640bfeb462c12c39108cbc70e036", "guid": "bfdfe7dc352907fc980b868725387e98edc2a89fb1a6e200ab1412ec87f9c9aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb5822db4c10e88763ad5f82a480d051", "guid": "bfdfe7dc352907fc980b868725387e98492b5866dd16c57c285b5e79c6867b49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cefe8874d43d0e9ed8daa4c038f1f530", "guid": "bfdfe7dc352907fc980b868725387e98e484c4f5150fbf8fabdad69f35a52c75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ebbfebfab04772192787612c48231c7", "guid": "bfdfe7dc352907fc980b868725387e98d15e2dd2c99fea1c18b18e69ffd1b703", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f435c429228c439ea9e2be509789469a", "guid": "bfdfe7dc352907fc980b868725387e986fee5e9c3343f8d1008352e701bc900e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855a0ddec6b446ff3952713a6946ed5ca", "guid": "bfdfe7dc352907fc980b868725387e98a3b33d70365c2c21c6e5ce5c403a6767", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888bc16f713f9d5a6cbd5f7dd156effd6", "guid": "bfdfe7dc352907fc980b868725387e98bc874e43a4cb78d8f4d1a0ac40c942bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98934cef9bf6ee848bfe3ec38d62197835", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9883d602aa8d8049fe342bc9fca30859a4", "guid": "bfdfe7dc352907fc980b868725387e9829d2b0b2b7d95a9ddf7e598d6ffc4db1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac32bd6df493b8cb16d09fd20b65fe3", "guid": "bfdfe7dc352907fc980b868725387e988ad79f08695ea6c2cca3c5d7d8b8e33b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af742425041db81293316a5220a7491f", "guid": "bfdfe7dc352907fc980b868725387e98eb8f6a719f355b17f23d8eb2e9a5d44d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d411c1b548bdb14595131e44ee4a83f1", "guid": "bfdfe7dc352907fc980b868725387e9816670788bc7f1e372bfade4c15b03b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898dabae675c35ef254fb550b20f041a0", "guid": "bfdfe7dc352907fc980b868725387e98838df245c5366c7ccd43779dddceac8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831e497ff2311d6a200615bad858a9502", "guid": "bfdfe7dc352907fc980b868725387e980cc73214077d885d0ddd9bbb802b0c2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a32e9b1b8c90fc4d563289021e07cf29", "guid": "bfdfe7dc352907fc980b868725387e987699d61d47db16d864283d0af14a2a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf82f34e5ef26e96c4c3e0038c6a5665", "guid": "bfdfe7dc352907fc980b868725387e98e97804a71a7eda4782f6d18bbb55dbdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba062c01e243419fc4a60432b2706ba1", "guid": "bfdfe7dc352907fc980b868725387e98d47beba43c550f9d5c74eaccd5ff08b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e46034315dfc41b454730ec1fbab354", "guid": "bfdfe7dc352907fc980b868725387e982130a66579435704115f7e00d09f53d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98418646f8b570c546a5bd3de73e48eefa", "guid": "bfdfe7dc352907fc980b868725387e98163ea20656902ae2aebec0d2b7d2c959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea80f8650f4c7ba550cc98560ba6bc55", "guid": "bfdfe7dc352907fc980b868725387e98c065af2abb2799503e76158497948974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e0a2b103443cdf4abe7d485c6520547", "guid": "bfdfe7dc352907fc980b868725387e986c074fcd043656e86edc370b80f2b6ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a872864d537c8464223fc41bc3349ee5", "guid": "bfdfe7dc352907fc980b868725387e98897022fb7c83452617bb5421eee661b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803c5917e9b8f93a2c5c7808170623f69", "guid": "bfdfe7dc352907fc980b868725387e98cfe537ef6fd48aebe0e7b866f6b1142d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833b9ec6c723b19d7e7cb8f74f3e05f27", "guid": "bfdfe7dc352907fc980b868725387e988830eb10a635306371e79be09704e0ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815739a9f88d3d097a9e72b6fa4580575", "guid": "bfdfe7dc352907fc980b868725387e989ac43435114fcf14579f257bc3f9c364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf44da2c11ebfb46c2ddde1b0252af28", "guid": "bfdfe7dc352907fc980b868725387e985cf8026ab77fe57445a9e11bc4c4c7ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e5e8f177213ccba88b54b18746145d2", "guid": "bfdfe7dc352907fc980b868725387e98ef071ab9cef1d242de5b1113119d33b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985742a771a7ba05cd3da36b20e08b57e7", "guid": "bfdfe7dc352907fc980b868725387e9852ddaf4c3962f25802ecf6e4d216c596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b0a928abd00cfd555ca3ff6fae931dc", "guid": "bfdfe7dc352907fc980b868725387e98abfa7315b06c10451197abd58e79d711"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b83301f49f12230e1cb57a77ad3446ff", "guid": "bfdfe7dc352907fc980b868725387e987f00ce95bbef4bcae9c2b6d4ab613e85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a20ce464bf472d5ca69ece0d19f1728", "guid": "bfdfe7dc352907fc980b868725387e981243c2de66d0689b6cc84852675e392f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b5e6b772b5490f3cef590085d79f802", "guid": "bfdfe7dc352907fc980b868725387e98a7f2e7eb44ba9c0b257d1bf51c2035a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5b8709fb21c35e430d5b45a9aa6bb7f", "guid": "bfdfe7dc352907fc980b868725387e987e1026d7e32253d02e2a13c87719a604"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987007cc3405330178079557acac28a180", "guid": "bfdfe7dc352907fc980b868725387e98fe57562c583a8a215f5783496b1ef801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982df4922dd4eb50ab19632a8ee4488212", "guid": "bfdfe7dc352907fc980b868725387e98aa36efef915c33b7edff59f6a9a0bbfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f50e508fdc794985225e66bb89a81afd", "guid": "bfdfe7dc352907fc980b868725387e98fd0b1420ef9ecbffd05e08127fc813c3"}], "guid": "bfdfe7dc352907fc980b868725387e981f18c3bdd543f0383a05196ffefabc9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e9808188f8afd5c22436d8f270ee53ec170"}], "guid": "bfdfe7dc352907fc980b868725387e98f59cae5e753b0a31876694ce85c47944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec476f90f1f6e616e836f5cbf5a8826f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}