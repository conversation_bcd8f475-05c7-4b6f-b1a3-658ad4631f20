{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aac11792083e4aae27c2bd9d608e50e7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5fbd71eefae1680ac3bd05207689e10", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982333deb2683a4274250d7a2e49f3618d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98643ad75501d562bc14480a5a08a77687", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982333deb2683a4274250d7a2e49f3618d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad3d066b358916bcd965ae85e73e97e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983653f73c86accaace882c0c9ded6b407", "guid": "bfdfe7dc352907fc980b868725387e98d30191a9f7620b9d248fac6a82aa25ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981424b2b7e020a4568d3bc6f41b04edd3", "guid": "bfdfe7dc352907fc980b868725387e985ab6f8b7e00dee2a08390684bf518827", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccae37c133870a4fa2d97ddac1565464", "guid": "bfdfe7dc352907fc980b868725387e983d5a5b68d5dd691adeb0b151fd2787fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd626f06a32c966d61c0559d95df0a9f", "guid": "bfdfe7dc352907fc980b868725387e98c7d7adcbff586833dd3b8c280ee763a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987673b7c0bd31a63b46c36dc624714469", "guid": "bfdfe7dc352907fc980b868725387e984e01980a573dcb05ab7757aa37403e7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867b064099c4d785e361353286b31883a", "guid": "bfdfe7dc352907fc980b868725387e984aed8d9ac9750260ece8dde237203f9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b31c1b111e6fc467b28caa70c6f737eb", "guid": "bfdfe7dc352907fc980b868725387e98ce670ace3c5686740d34a31bf996f767", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7b96c9304af383aa96e261c73c67f4e", "guid": "bfdfe7dc352907fc980b868725387e989360bf1e2f232b2d585c18af1ff781ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dbf2516f9bc81259b316773151a6215", "guid": "bfdfe7dc352907fc980b868725387e9893d8a61570621222c65d333fa09d8dca", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985f7512a340b40f6c76cd5b69c09921d0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e153f6752a928c682828997230127b80", "guid": "bfdfe7dc352907fc980b868725387e9843ae352536d9444cb390d4feb16c773e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989330f9325f6a0a8675cda6b8ec3416cb", "guid": "bfdfe7dc352907fc980b868725387e984a15a43ee2c929603880e19620fee4a0"}], "guid": "bfdfe7dc352907fc980b868725387e981214057bf558ca1bb3d52fc8dda1e410", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e98e7347fb78e0eb94a8a638191f93bb7e7"}], "guid": "bfdfe7dc352907fc980b868725387e98ec8e931c96de27876ddbd3473cd39fe3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986ae53909b5b5987784b563dbb0114b13", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e986b5dce6c6780014ed3a7c975569e69fb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}