{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988d17b0532903fc5725bcb4b443ff4cd0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989754180dd216f1b4968ffcd48fa51406", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a223f87e03558f10d2787f3f834c1812", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dce598c7c6fb25950c1469de3b8bd5f8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a223f87e03558f10d2787f3f834c1812", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9812816d20a6c226ddc1e5b0ef13bf4a68", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9884ba547cbb2e4e38ca1533f892247eac", "guid": "bfdfe7dc352907fc980b868725387e98aff7742a4d8d961b7b399df60bed431f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98932da2fa6e1b149322a4e2ffaa5ce9a6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981eb36ac24baa32b8443b21e59dd972b2", "guid": "bfdfe7dc352907fc980b868725387e982634ee85336cda84b268d6723ff94942"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9ca7a6332770dd20a4cff46b51a50bd", "guid": "bfdfe7dc352907fc980b868725387e988d3e2b8a6b4ed3ddf3162fe585b49c69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4c23e8337d3c35bf6731a64f1e2f2b5", "guid": "bfdfe7dc352907fc980b868725387e98353cbf9aab8dd69785d81d6939b0f877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980070816f6be5cee5713d79b20f29aee3", "guid": "bfdfe7dc352907fc980b868725387e980ceef761d2b7132b57eba9b07810ee5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c1463486de272b38b9af95ca618757f", "guid": "bfdfe7dc352907fc980b868725387e983dfdeeb1c1bf351372644d2302c2bd2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852a6b29a087edc759cbfcf8d08747e6e", "guid": "bfdfe7dc352907fc980b868725387e98cf2d2d3da8c54e5a7728bb2ef4847895"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842c3fa5757c07f64b6d71fb5dad428a9", "guid": "bfdfe7dc352907fc980b868725387e985f2f94ea23d2a294980bd0d0fb7feddc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cf9b2992937eb39070a9fae2209e953", "guid": "bfdfe7dc352907fc980b868725387e98a9ad4812dbbd4e338b841af9bf128643"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851b5a9e89dd2e2d49c4f48eec55e969e", "guid": "bfdfe7dc352907fc980b868725387e98871fa47c92cae5f29de151836313cea1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eada65550cd54285f621477bc9ff234d", "guid": "bfdfe7dc352907fc980b868725387e98cb87f1c2f13e0272df3d3293a8b8a9c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1278c236e97fd381828ed02a9e15085", "guid": "bfdfe7dc352907fc980b868725387e98f8b71708b21e0d3e832891368a83ba7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db0dbdc0cc9d6d42f59e8bd86e953dae", "guid": "bfdfe7dc352907fc980b868725387e985ed747b0a4b79d7e8f2ff569c7fd66fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c32a83bae527d183519baab89abd9ae", "guid": "bfdfe7dc352907fc980b868725387e98c13b8b0c7d93df61892d0c58fbba53cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d43ffba4cf4b9e324ebfdb93f44ef12a", "guid": "bfdfe7dc352907fc980b868725387e98dda1aa3ad2534a813f066efd4428df64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5e96e016dc88a176bf2bf4bc4293868", "guid": "bfdfe7dc352907fc980b868725387e98d373b3f3e775d940630dd20f6b30870b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98342107fc89768a46564419194b53dacf", "guid": "bfdfe7dc352907fc980b868725387e986eb2830080d70d8d94a44e46b6dc3472"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2496ad80e5982af9cda7c0dd41b5c3f", "guid": "bfdfe7dc352907fc980b868725387e98d497488cf178178ad0164062b8e26852"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858199bfccd58a2cb3cd7e48b286dc571", "guid": "bfdfe7dc352907fc980b868725387e988addc686b91cb195d2791ba57b9b9be8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4f3af53baab639f9828b868d8b18e13", "guid": "bfdfe7dc352907fc980b868725387e9894893433b9057b4ff0c8fabca4a0a373"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc4c2d0a59230d414ab66e28ebec27d9", "guid": "bfdfe7dc352907fc980b868725387e98157ed4d821a56f81b06a0d18b640b024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a93185849c74ef6447b11d715aec01ce", "guid": "bfdfe7dc352907fc980b868725387e98ed9545f2715449f81f473234c3004e45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da244598111a9758d1b0966177432adb", "guid": "bfdfe7dc352907fc980b868725387e985dd31ffef71f09bbdea09407e4a24d8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98412eaf9ccd742ef6535e0fa8bbe6985b", "guid": "bfdfe7dc352907fc980b868725387e98e3668858ae0051a7cc047c58822fbf53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc53d32f78950fa4dde40d1bd034c468", "guid": "bfdfe7dc352907fc980b868725387e98db06b300c11f511beefd66c17d8a8c26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0827602d4271761e400135e88dd214c", "guid": "bfdfe7dc352907fc980b868725387e981c97fb9302422db128747ee46bdac94c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe5815ec220aba5935a7c10773a8e0fd", "guid": "bfdfe7dc352907fc980b868725387e98949025040df040fee908e1d3bec30b60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c0dac07167abbcd816cc1845b51f2f", "guid": "bfdfe7dc352907fc980b868725387e987e65541284ebd2aeb84db7414b7aa9fd"}], "guid": "bfdfe7dc352907fc980b868725387e9824da52dba992ff151f1f0243d37f282d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9858388e919851253d7be5466beab71104", "guid": "bfdfe7dc352907fc980b868725387e98bc7546a2ead09b7777064defa7a1aae7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98318a076cda42454f0efdc7df07629b5a", "guid": "bfdfe7dc352907fc980b868725387e98b12fad0c17db2b68dbe240c3b7a8426c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e98f72c42cbc5181ace173beba8fc78562b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5cae867c149c07ad8020402053fc82b", "guid": "bfdfe7dc352907fc980b868725387e980df8e2f96f6784fba2643372d64c38c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea565b399103566eee5262dbfd427ed0", "guid": "bfdfe7dc352907fc980b868725387e980703c00db409c450920b7ffc0ba555cd"}], "guid": "bfdfe7dc352907fc980b868725387e98c0f269f46f222e52e7facc72c4c5a7de", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e19310ba22740df295b507d0c0a10ecf", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e987da7a211729edc90489d7d41bf30a9ce", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}