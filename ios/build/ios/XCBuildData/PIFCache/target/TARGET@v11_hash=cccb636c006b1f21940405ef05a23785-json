{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5e3d43fb8cd7b6072f5409e6e24a076", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/GCDWebServer/GCDWebServer-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GCDWebServer/GCDWebServer-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GCDWebServer/GCDWebServer.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GCDWebServer", "PRODUCT_NAME": "GCDWebServer", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9820a2cb7398185ca719ae889d48923b7e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9807b8727e59cb72b052b675e38cb83a2b", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/GCDWebServer/GCDWebServer-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GCDWebServer/GCDWebServer-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GCDWebServer/GCDWebServer.modulemap", "PRODUCT_MODULE_NAME": "GCDWebServer", "PRODUCT_NAME": "GCDWebServer", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845584cf9911842a36472dca74a350c68", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9807b8727e59cb72b052b675e38cb83a2b", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/GCDWebServer/GCDWebServer-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GCDWebServer/GCDWebServer-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GCDWebServer/GCDWebServer.modulemap", "PRODUCT_MODULE_NAME": "GCDWebServer", "PRODUCT_NAME": "GCDWebServer", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fce723c8686d8574addfefe416592132", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989c809b7b03a6040e9dd6eb4880b6194a", "guid": "bfdfe7dc352907fc980b868725387e98f9cef44151710a8578a0e8b5a6757972", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980387682aea3bad49bc0767c588863ab3", "guid": "bfdfe7dc352907fc980b868725387e984af319b1d61196c8e1f55b3813851828", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dafb15240ec5898ab877f169630b4c4", "guid": "bfdfe7dc352907fc980b868725387e98593981748603e5f7470eb5421d703334", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f9a83784051dcba3f5acfd991a286bd", "guid": "bfdfe7dc352907fc980b868725387e98fc9d1809fa01319c86f15be5510a78a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b20c4006f0cf3df287734b4fe08241d7", "guid": "bfdfe7dc352907fc980b868725387e98d01c8a52e4bc2beb60a23c170956c0d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a30268ac3c5b4238538d78b06b130f10", "guid": "bfdfe7dc352907fc980b868725387e981bff5b75cd1c4b75fba8a13cc1ffeac6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982392816cb6332e797d0c78ad5a85849c", "guid": "bfdfe7dc352907fc980b868725387e9869bb8a9912b12534c2f08ae56dbe1b68", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4a4861bd52e06a82560f335097c2049", "guid": "bfdfe7dc352907fc980b868725387e98651ec35877cef5c7842a8740956b505f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803d6e513155ed009337f5f8fe9630f2e", "guid": "bfdfe7dc352907fc980b868725387e9815b146573554ecbd23ce9ef7d8206543", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844f080e5df892a4b2862b677c064a61b", "guid": "bfdfe7dc352907fc980b868725387e98f004959d7f9b4ed02659fde7b189cbc1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98281de9f1e1c022d3caca65b487fd19d8", "guid": "bfdfe7dc352907fc980b868725387e98885b5af608e3716c0eeb84a193aed92d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813e2664a47b0769c1149ef50a961e92f", "guid": "bfdfe7dc352907fc980b868725387e985150a75806bcbc2e76583b29545a3684", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899de39bc9cb8dc455e46e6325eab8ddb", "guid": "bfdfe7dc352907fc980b868725387e981f80272216a7f342e1b5582202cfebf0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3e25c00c79e8f4f663ffcc23400e2ec", "guid": "bfdfe7dc352907fc980b868725387e98a81cf48cae4e9aa095af5e5186237e62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1bea9a3cf0b19f75395fdad0dda640d", "guid": "bfdfe7dc352907fc980b868725387e98ca82a2a37a19117ba9f8b8c44762c0c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9cc8a5debabc94bbb890ff7983805b6", "guid": "bfdfe7dc352907fc980b868725387e986b0e9522120a8249f07177f38676f343", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981220135ca6690450fa04c75a6cf25f15", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989a5d21b2c92f4a19b3c39bbb49efb1f8", "guid": "bfdfe7dc352907fc980b868725387e98a7d71889ae54b4e59207075d95e4a490"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810faeebe527fcf2e61815e5ed2ddbf19", "guid": "bfdfe7dc352907fc980b868725387e989a2034edb45398b123f72f7a05c4a288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989563e9d9a718683343177fcc7faaebff", "guid": "bfdfe7dc352907fc980b868725387e98dfc8dace853a5ce5ecccb8788c8c79d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8bb5890c4b76556d10904dd0950ee29", "guid": "bfdfe7dc352907fc980b868725387e984ed5e8852a8d64a53876235537415c54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984067ee8fd515a8d4ec6dd0722dfb526e", "guid": "bfdfe7dc352907fc980b868725387e98dce16ed5a025a752a5b3d53e0f605bef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987624f0b6819be8b73908c9387951cb26", "guid": "bfdfe7dc352907fc980b868725387e986e3b34a7c72c36c644ceb82e2cd7eeb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d36540029e75797efd165e1e9944150", "guid": "bfdfe7dc352907fc980b868725387e98996d41fb8619bee1a5a1c64c1d63b98e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980be3f1181e03922cf587690c91783805", "guid": "bfdfe7dc352907fc980b868725387e9886dad12b9b588c9f9735be6489779885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d27574a0b5d09edb20047e5c6056c2ce", "guid": "bfdfe7dc352907fc980b868725387e984b317917d0edeb5d4ad967e4d3ab3bdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cafeacc20f10132fbf9bccb5dc492a38", "guid": "bfdfe7dc352907fc980b868725387e988831a512a63f59528340d53b4d0f94d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981377b41d49891f719b209a0adfad2be3", "guid": "bfdfe7dc352907fc980b868725387e981b4ac6ff2fb493e93a7094e86bc28a76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98990fd2304c2e4eb7edb71798f897bf66", "guid": "bfdfe7dc352907fc980b868725387e988bb0c7ff74e92cd2b77cdc9c2125aca8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b3780dd9a88c17c97d8c2cf8aef205b", "guid": "bfdfe7dc352907fc980b868725387e98a89679ba4a0c8c4ac5da389b1037d52c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcc2d259a22101e773d2ff7e9f7ddb24", "guid": "bfdfe7dc352907fc980b868725387e98912a317c31e1aec549ca80a68788c255"}], "guid": "bfdfe7dc352907fc980b868725387e98cc2d13f408a8064d8a6b28875feb99e2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eab48b5738a00c363d37a75433fd852c", "guid": "bfdfe7dc352907fc980b868725387e98b5ae0e6e0fdd2521ec19a7f8a89252e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e988ddb8d5b5e6c0d5e9a3b3e52fc5fabdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987285310eadd50bae519f4f791934c9b6", "guid": "bfdfe7dc352907fc980b868725387e98dbb8fd4ab7c00e66f1681d29cad47a45"}], "guid": "bfdfe7dc352907fc980b868725387e98bc805078eb0d455d7e9a0b7639826924", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e989fc01a956173dc7f0ec6e92897942b9a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98ae4cba9dafde33c1f8a32ad5773d6cd3", "name": "GCDWebServer", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ea940bf2ad9c0f24726e4c14aa315ff6", "name": "GCDWebServer.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}