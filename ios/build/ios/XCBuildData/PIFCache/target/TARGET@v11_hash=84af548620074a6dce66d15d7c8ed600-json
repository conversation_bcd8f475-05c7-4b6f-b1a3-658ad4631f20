{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f4c23c077de453b2e6f6478d512f4077", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b1f7dd415bf6eb878bafe536ed6947cd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9834c14655464aac6a937882ffd10c9048", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98536291ce41f742f87ca504ce39101eab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9834c14655464aac6a937882ffd10c9048", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982399795bde80415f0ffc2feab7439a32", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e6c0d0d4abbabdb264d2bf0cc701577", "guid": "bfdfe7dc352907fc980b868725387e98f2212ca3896abf6396a3285c4324eb2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e7911d3bb3ea301ca4be5f80e226217", "guid": "bfdfe7dc352907fc980b868725387e98d4db48483fbb27aef8a977642270776c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1fdcab77110557affdeef797661e03a", "guid": "bfdfe7dc352907fc980b868725387e981506a1c2a0790a53068345ac573d2405", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8f7e9ebb5e85164e8faa969a52b1a00", "guid": "bfdfe7dc352907fc980b868725387e98fd41e03b4589610946c9c0302f4809f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cb32b5b4234c06b312339816ecbc78d", "guid": "bfdfe7dc352907fc980b868725387e989f7cb97b1268902294596b5a4b0a932b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1c763d7592260fb4961373adf6c5e82", "guid": "bfdfe7dc352907fc980b868725387e98cc454b78e79377f87175c8f02d629532", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dab0fa684b43704fb68dc30eac79451d", "guid": "bfdfe7dc352907fc980b868725387e9859211cd79b0c0f98bddbb9d4cf660dd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981deadefc7242cd33edb9c7794b3830dd", "guid": "bfdfe7dc352907fc980b868725387e98841c337904ab7cc7e427acb6b18ae304", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5d895c23dc614627e2f2be0ff60a933", "guid": "bfdfe7dc352907fc980b868725387e98a826afaeca4ea6797a350aa1f1fc2de8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eadf32778fd6ceee14330c05b5552aa6", "guid": "bfdfe7dc352907fc980b868725387e98b132d9cbcf9332dc4299c85a0d1d889a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e23db330c8596ed1ce7976c931fe96b", "guid": "bfdfe7dc352907fc980b868725387e981758de07e6c5984fb365325f3bb2dca6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fc39b9e32ae92fa7adb19414561c811", "guid": "bfdfe7dc352907fc980b868725387e980687ac568bf16ef0d15818bf14a8f8e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859ac795c9fe578526866ff372b6cc72f", "guid": "bfdfe7dc352907fc980b868725387e98cb78a9b372e02426fc06784cf140298d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b3c79fad436ece762424612b39fcb75", "guid": "bfdfe7dc352907fc980b868725387e98363d498eabcfd862dec0a6a26436c916", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984adc9dcc8594c1066e03862e393eba8f", "guid": "bfdfe7dc352907fc980b868725387e98afcadc92b71bb7d126b91651f6cb07b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8c8d3afcba97a660ea32fc3d1ec3b94", "guid": "bfdfe7dc352907fc980b868725387e98a72a6b1f3e39f9adeca357d35e19cd6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c91b960475e577fc03e620a5f39052d8", "guid": "bfdfe7dc352907fc980b868725387e9857c6f7aeb48c1df20352e54253940812", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bb823141bca3af0cf36e9a3033593fb", "guid": "bfdfe7dc352907fc980b868725387e98d762ff6ad423eb7e4365004676559f53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828d592ce38570db3f749fcde2ab973e0", "guid": "bfdfe7dc352907fc980b868725387e989e823d31c81de14ea560b76651950728", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98013992fd249339b647c4a9e1efa68956", "guid": "bfdfe7dc352907fc980b868725387e98c50e18d74a131626830065946b7943be", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcfb8e76a0061d0f21dbc83272be5bf1", "guid": "bfdfe7dc352907fc980b868725387e98354ae95d0262ba318de032ecddcb6570", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846eafb7c3c3a261e60ce1328735a9b84", "guid": "bfdfe7dc352907fc980b868725387e989acb781cad01fd4d5ea90c785db6d651", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985a74f1a4d08129cab394c0de3eec4067", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9821f6ae8b61539e885d168f92f08be418", "guid": "bfdfe7dc352907fc980b868725387e989363cbcf6b1d8019babe61e955a29369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f6f51be763c2d4744b6a1b7beb697df", "guid": "bfdfe7dc352907fc980b868725387e9897988bfba5a77b9d6f35e845edcb97ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a56e6a9cdfac2e31e64b3e4c3e10bac8", "guid": "bfdfe7dc352907fc980b868725387e98cdd7cdb71bdfa08a38d6b9bcf8a6f398"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa19439316fc049dcd4b520a4bacbd05", "guid": "bfdfe7dc352907fc980b868725387e98dbfa74e79f0da7a6e67ea8469a35a623"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a04bdb0a7719c4565f48b7de76b5ec00", "guid": "bfdfe7dc352907fc980b868725387e98151289cce10e3cfc9343474b3d481076"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838d0349b2eb62f63382c1d7e7e7754be", "guid": "bfdfe7dc352907fc980b868725387e9862f25e7577402d4a3760238b0e19896b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885a741d2969cec375cf526cebd988577", "guid": "bfdfe7dc352907fc980b868725387e984f37c6332100ea6e841d0993d41531c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8288ee9dfe6b133f83dee75c66ef20f", "guid": "bfdfe7dc352907fc980b868725387e9821652b0e1d1a6210109d020464a95a12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de7ebc2495c14ac88e01fb1a0b8c81c", "guid": "bfdfe7dc352907fc980b868725387e98278397b8512e6c6bc94b71841815e736"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5306cdbc86fd1d943a45742869c98cd", "guid": "bfdfe7dc352907fc980b868725387e987c961d2e8ebce6e571375c9ed85e1060"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ad8a0bfc2a89d56ee07dccc60a6c6d0", "guid": "bfdfe7dc352907fc980b868725387e98116251fe9b8855ee4f25d30ff908371b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981656a0136a8ace618b30df0061b0afef", "guid": "bfdfe7dc352907fc980b868725387e9886f692115932874f7fef58e231aba855"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eec9714b21c742a550c6ec5e84af394", "guid": "bfdfe7dc352907fc980b868725387e98bccfcc7c8b3b9bc2c5f09c75ba5fe982"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e5d74a8e1d190e521ad420cea328c08", "guid": "bfdfe7dc352907fc980b868725387e9867c04e98ba90c575e2dd6bfd93965994"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871638c499db9d19bca55a532d85d5da1", "guid": "bfdfe7dc352907fc980b868725387e98d8053893ba597a5f3b7fdd232845767f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885161efdd719fe33e9edd662275c385e", "guid": "bfdfe7dc352907fc980b868725387e988d838788f8ab75df1c9fbe82a44da8e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c90a66fe053269415ad6a913ff0ff5ab", "guid": "bfdfe7dc352907fc980b868725387e98817e0dbfbf05c1e2f91e4285f9470d05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853b0eef4a935671fc0eed5e03f6621dd", "guid": "bfdfe7dc352907fc980b868725387e98d00ee9984c6f9c6431521e10407bac16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b23c700c575bd3d2b73cf4e003c60875", "guid": "bfdfe7dc352907fc980b868725387e98ebd65c6124816bbb69ab6d9c6b1679ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896537e8aec31fc080beb2b5f9ed0c21c", "guid": "bfdfe7dc352907fc980b868725387e988c09d8aeefc475212395a9be510d93d8"}], "guid": "bfdfe7dc352907fc980b868725387e988b277041fd0772e7823efc8f7be9544a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e9869b246439cec2253f64f3ba72a9a7ec4"}], "guid": "bfdfe7dc352907fc980b868725387e98c9ceccc6751ad9236f0a2da403419905", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ff303689647c51f69038b5cc1787aec5", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98b5686e77017b62ee98889c5a5381478e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}