{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ef528f4b03b15abaff11072a726a15da", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98026dad0ed17a72d38e654ae409bd8dba", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987006a1f1afcb3a6c956095d10d6bf02f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98858821de8a72789d536805416b45b2af", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987006a1f1afcb3a6c956095d10d6bf02f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f0e2f1fb7eee7c78ac5e925d2068b24f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986428e1681b6ad3ca0ebae8bc3d61a00f", "guid": "bfdfe7dc352907fc980b868725387e9800636a112723e56bc254771427f9ba2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4b6114b3de3ba9ca7a10f2b909de717", "guid": "bfdfe7dc352907fc980b868725387e98edf080546e97e6a2c6d5098683e5b912", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e81944102d4ed19f767287cdf78c6601", "guid": "bfdfe7dc352907fc980b868725387e9832092e9a3d239212b3c96898136a0e8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcd37c6e94afb7810d7002d01fc359d9", "guid": "bfdfe7dc352907fc980b868725387e98cbcf89dcefc86db58505d17bdeac4819", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989140ae42cad86db254646379947a849b", "guid": "bfdfe7dc352907fc980b868725387e985832fc6d41f0c6a08c87a6cf38f5d05d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9629577f659a7a2cd2fb75bdb81dad9", "guid": "bfdfe7dc352907fc980b868725387e987888ecc071b3bdc7d4ad9b25fdc3b15d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989978fc196d7216828415bde6f5b2c699", "guid": "bfdfe7dc352907fc980b868725387e989862fff0358714db343226241d44c4de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c31e18d2395bb51f355c16d127506c06", "guid": "bfdfe7dc352907fc980b868725387e98bf83010a7241b287241ea91cdac29cc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8c8d44a22512eee8ce20500381489ef", "guid": "bfdfe7dc352907fc980b868725387e98c0d4985d8f669bab3d7a36054090cf93", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f3bfde7d904a580ff8f8d82823c05297", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9872c3dd5e345da556309461c15c4c438c", "guid": "bfdfe7dc352907fc980b868725387e98d7f95f89e0619f58698b7b5ac976a708"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa029e829af299165f6ce4c85807ae95", "guid": "bfdfe7dc352907fc980b868725387e985cd5be3e9401e72ecf9b9c675e0e4a7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801460cfdb9d48d528d1584883102a07e", "guid": "bfdfe7dc352907fc980b868725387e985781b66c4f033e765086f498cfc235a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98555baf2d81b7d8c62df52cdf51346163", "guid": "bfdfe7dc352907fc980b868725387e98b4a3f2599210ea96bb0d7431390645ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d7f3fcc42a936a6408896b7b6ebe503", "guid": "bfdfe7dc352907fc980b868725387e98853069bd3316cd334a9bcf3aa0d0fb55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd9ee65046efc5c870c21b71c20dfb85", "guid": "bfdfe7dc352907fc980b868725387e98779bffe358ca7a632e0f4d38da9368ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fed79179e1f5344a63896ffe8e032229", "guid": "bfdfe7dc352907fc980b868725387e987f4a9f09f2a1edda4c0c5088f2a9cabe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac5898914d66b3f890338ea1ca84477c", "guid": "bfdfe7dc352907fc980b868725387e989af1fac3b56bb6d1a674e486308be7e9"}], "guid": "bfdfe7dc352907fc980b868725387e98ee43c81aebe6258b8ad40e12a2c81fe0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e98122f052902c4d861577f35c503e240f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c81c87c9ae76013da3d981243a0bf06", "guid": "bfdfe7dc352907fc980b868725387e983f77935d0ba1bf09c10f214087d36472"}], "guid": "bfdfe7dc352907fc980b868725387e98316971915a5787e3689c542fa43773b5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9850005db1b51b5e5f5076243e1a83d611", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e987990cf146d0462043024a594cbb49138", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e980b0f743323b638aa27157d8d07e14978", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}