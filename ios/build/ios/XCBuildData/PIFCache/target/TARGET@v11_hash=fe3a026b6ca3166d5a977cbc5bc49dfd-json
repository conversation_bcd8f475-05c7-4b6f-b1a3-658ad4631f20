{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d19779e851d662c3a01ad58ad7f1bf2a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ba6fd30a2d2c0667ddeeb471b87f3c67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ecea8f3ae2f9813f71fb46e366f62940", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985872c6b49f1d92292872024b383071ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ecea8f3ae2f9813f71fb46e366f62940", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869811edde2fbec4b998cd7c872ea4b10", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9858805ce5796cf3c5b1e8ac0d58c078d5", "guid": "bfdfe7dc352907fc980b868725387e98fadb486075b285ced26dc074123c7de0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b05e659eee64b71a3028006bef90fdf0", "guid": "bfdfe7dc352907fc980b868725387e982fe32e357c24218ffd83a8755a5aef4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d626e40086622a05e3579848bd79522", "guid": "bfdfe7dc352907fc980b868725387e984e8ef2d0871966b0e1c3eb4e1dabb288", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ba45de86ec334fde6dd02cd6995e446", "guid": "bfdfe7dc352907fc980b868725387e98f617d1f5eb7c59ea0dbb9b965cfd32e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e8a6b9c0bdc0aa3878d76836967bcde", "guid": "bfdfe7dc352907fc980b868725387e9845e19ba25df26c3ccc091052c94434e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98134ad64ff3e58a8db2019e7cc8d1dadb", "guid": "bfdfe7dc352907fc980b868725387e980f66a8466556e02887e640a18a15d2a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878e6f25c0552373b694207d73726bace", "guid": "bfdfe7dc352907fc980b868725387e98c7c54b07bcddd6ccbf7ab80cf5a1cd10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fd415bb04caa3d8bef49e73ceee3f56", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980723c515b1813caea066b0a4d94ce3ae", "guid": "bfdfe7dc352907fc980b868725387e989b10e4f1a2392b46188b46a696fe6f35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e07e9f32b926286857e146ce70ac7cca", "guid": "bfdfe7dc352907fc980b868725387e98dd654257a1fd0c6e921147a42f67b5a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe041739c77ab4ff36cba85666c01eac", "guid": "bfdfe7dc352907fc980b868725387e98a45519d9b7df12fe88c43fd3734edd28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a625e0e59f0ec1a2139fddc4d7c2f334", "guid": "bfdfe7dc352907fc980b868725387e98f682da9a79143524b5f497de034aab22", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9856e6367461b24a2765d748a9c7bf2878", "guid": "bfdfe7dc352907fc980b868725387e982719fbf133fe11908b7646a8732e8c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987700c49785093b5f17314d581ecbf836", "guid": "bfdfe7dc352907fc980b868725387e98e7f1adcf8bcdd041993e95b76576e783"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98882076b560f3e2cd7f6fbafb3f4a0e08", "guid": "bfdfe7dc352907fc980b868725387e98c8ecff818db0dd67fdeef6fae4daf174"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9ca031eff556f792910b8794c3bdaca", "guid": "bfdfe7dc352907fc980b868725387e98c4a5745524eda0f2de3c7aafd595192d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f32d841e3a7326a0578ee2cb3ac90ea", "guid": "bfdfe7dc352907fc980b868725387e98028d032725aacbef77923adda72dc897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d332acb3d86c9c3099dc1e1d8d75047a", "guid": "bfdfe7dc352907fc980b868725387e9862752dfe48b852effa95e644f27aa841"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811e6cab969169ec24d4a94181857ac7e", "guid": "bfdfe7dc352907fc980b868725387e980d82d6002380c17426352d2b6dcc403d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98785aa93dd93aa26d8da5d83034b111fa", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842452ca085420a2afcae37254b8c6fe3", "guid": "bfdfe7dc352907fc980b868725387e981104cbc90d9acbd26870d72ac58f83e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827cf2f4f33d7cdfc299a01940be43988", "guid": "bfdfe7dc352907fc980b868725387e98228a73a0dea2578b100514b934e774fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e494fdeef327fb856f285d843064d1da", "guid": "bfdfe7dc352907fc980b868725387e98f01d1de579313563442377b603c7f1b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcc7a7074931768114f09f31937c2efa", "guid": "bfdfe7dc352907fc980b868725387e9898e57582cd28f465ead1f88705177fee"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}