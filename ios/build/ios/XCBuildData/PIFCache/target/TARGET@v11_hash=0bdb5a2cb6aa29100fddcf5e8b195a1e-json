{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cdbb9600a73c83a9113d88842f1f083f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b89882dc28a6d5c860523e82f554cc84", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b955b28ba22adfd77de75d238d6ed077", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e29cd749ce03643e9abbafc9d7ee4c45", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b955b28ba22adfd77de75d238d6ed077", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982fd3c6a7f4d84d77d3348746052d187a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984039c500ead76646bd03d6cbe3d84839", "guid": "bfdfe7dc352907fc980b868725387e98807bebc10b9b2347b3c38ab76dcd39c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cb1bb2dc8d86d128296a43f86cf4e48", "guid": "bfdfe7dc352907fc980b868725387e9855236db105a1790362e0f3f85da2820e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f978f0bcd85da9c3f3f0ea66389fce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e9f1356a5f9a9926b8d12ef757058aaf", "guid": "bfdfe7dc352907fc980b868725387e988114bf115703b57c8ae21d5409dd19e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e66e6755ff975cdd001438743650e53d", "guid": "bfdfe7dc352907fc980b868725387e98b588a7e8d8ddd4478d80982232420613"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d03d69ce01409cf94c659be7e6dbc825", "guid": "bfdfe7dc352907fc980b868725387e98c25bd75abc455f60626b1b6d2e12965f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c2b544ad3866e2eb034aa8af60fe28e", "guid": "bfdfe7dc352907fc980b868725387e9886bb1e038f2ec0e9b0706aa88efa09e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b6f569e86d6d010ed6fe4856aff731b", "guid": "bfdfe7dc352907fc980b868725387e9867d7f020d92c0259475431094f026daa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98292e70d513bfac2d973dda573d9db987", "guid": "bfdfe7dc352907fc980b868725387e983cbfbd91e9e8b5ed232e4a3e97ee6ea0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c0dd5678697beae001aa6e4aea4357d", "guid": "bfdfe7dc352907fc980b868725387e985677e626286185176d39b4c7a3131d79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a48555b5e68628166c24e48991c87bd7", "guid": "bfdfe7dc352907fc980b868725387e98d470e112a689c44d32d123ec1e9a49dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98678c2673e2e1dc86fb5257c6af4df2b5", "guid": "bfdfe7dc352907fc980b868725387e98ca6c1fe797300662ce54309c21e1f90d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f68087b5aef0abb60f189405517b4e7d", "guid": "bfdfe7dc352907fc980b868725387e98e2db67651016b0e5a242bf079bcd1b7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf9184a540cb3bb10df99311febe1b78", "guid": "bfdfe7dc352907fc980b868725387e9893f552456b99ece4b3b598b98c2d0951"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d2dd67ce5a57f0be464fa39d2ca54f8", "guid": "bfdfe7dc352907fc980b868725387e98e64e2f7a87cc703185c2211316deb4fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867da0114b5009f0275d10dea5a26f4a4", "guid": "bfdfe7dc352907fc980b868725387e98831f9cc115b07d97e9aba4500b47d900"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8b041ba778256f27dcf3e142a0e4e4e", "guid": "bfdfe7dc352907fc980b868725387e9813af1cf6f1aa063237c27f62b8da4f4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a220fd0833c0116748a4685c00ab9637", "guid": "bfdfe7dc352907fc980b868725387e986ed5364d2e170f445d04136ca20dd611"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed455bc002daa6e3764fdbd5ffa57c03", "guid": "bfdfe7dc352907fc980b868725387e98e480306e9ebc93b19dbf732f19c8e307"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cf2b01153dac428b136684849a2544d", "guid": "bfdfe7dc352907fc980b868725387e98a0b529fc765425975daa2152012b3d31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983adea20196b04507f483d1fae24bf23a", "guid": "bfdfe7dc352907fc980b868725387e988ce54280e8249a8ef34a9f874fa2a3d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd333c48ef06f78be85120dcdb4886cf", "guid": "bfdfe7dc352907fc980b868725387e98c4eb53ccf283dd9fadcbc4ab328696ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4dad939a54d508712ec46e942e21f17", "guid": "bfdfe7dc352907fc980b868725387e988c7b5a9b08093316782f2062a6bb914d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeff14b0a647f688a1b99349bcce0d92", "guid": "bfdfe7dc352907fc980b868725387e98023d74f18fb22dba3fb8cd08619adb5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b71c7a27525644766037d42bd024bc29", "guid": "bfdfe7dc352907fc980b868725387e9845ac40f0b799c7c7d7c8542c2caa56cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989db96c97b7e8d87c590c29c78b8418cb", "guid": "bfdfe7dc352907fc980b868725387e98c518fd94f66a19215b72cded5acabcb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ca32f6052d862b5fd8806af850c0f1d", "guid": "bfdfe7dc352907fc980b868725387e9865436ccd5e5292ab3eb9a2c726d4fd55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848c8cfd406e9f611eec1ef19384a91b3", "guid": "bfdfe7dc352907fc980b868725387e98c9c0f8cc39cd1f4c2791c1b6bcd95b24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d2121709e825dbde52d2479e6e0213c", "guid": "bfdfe7dc352907fc980b868725387e98222e67f8fb32506a7edcf8a0ac719b69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980804b17d8a4689a60ec55722944d8473", "guid": "bfdfe7dc352907fc980b868725387e985359bf3f299bad8a26efcc1796d7c1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985678fd1acc3e7e9ac47c14eda1308313", "guid": "bfdfe7dc352907fc980b868725387e9894b1e9a9c15f1d4d6dab730cad9a7356"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849f58e49a029cb5878198452734943b6", "guid": "bfdfe7dc352907fc980b868725387e981eed0494494d3067db554c50722a6670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2df0f6b8dade23e7e929ac412edc2c8", "guid": "bfdfe7dc352907fc980b868725387e988b7cd12bea006b45c3dbb4e16996e9d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802ab882e10c7ef5be80f6e858c34537e", "guid": "bfdfe7dc352907fc980b868725387e98d7495c6870d2ecefef04b9c19bbca9b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876ced7829638e82139d03d8df84e9eee", "guid": "bfdfe7dc352907fc980b868725387e983bd56d9a4345e817c88f1f030a933016"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef37525e12b57540a48f93ed726628f", "guid": "bfdfe7dc352907fc980b868725387e98faeeab2baa047c58c10f40717f073aab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2b45f91f51c27457403cefd2c8bcac7", "guid": "bfdfe7dc352907fc980b868725387e98cea48adeb08a5939b6bef50534e25be1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864fa826540c7861cb3358bf8dfd06563", "guid": "bfdfe7dc352907fc980b868725387e984c86b1becfedef31481dcbad2b17a5c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0f620b021c1e28797788a6a17ea5ea8", "guid": "bfdfe7dc352907fc980b868725387e98da3e64c4fb8555d91305261dc4ff94d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d98cc6f88de551f2697244c05456f12", "guid": "bfdfe7dc352907fc980b868725387e98bda20fd5eba9b97c8a836079351c8cd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983243abb001e1b634f9d0e2d44a534729", "guid": "bfdfe7dc352907fc980b868725387e9806a749e93be9d437dd5f0ddf38711a74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875ed440b72cb75e99dc4008c4e424a45", "guid": "bfdfe7dc352907fc980b868725387e98fdb2b1d48c92bc84d4a594cf3335c801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874714acf39101055ef3aba01a9c211ee", "guid": "bfdfe7dc352907fc980b868725387e98e15c16685b281da13549a9fa2a0b8813"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d03d2d2518df11e4a5143cca0c93be7d", "guid": "bfdfe7dc352907fc980b868725387e98cc497b3c5b4a04b925785d634ff1e14c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98000874f397c032522b86b072dac89b8b", "guid": "bfdfe7dc352907fc980b868725387e981ba2cec9e73183ea8decd900c0ab5b8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981609894d3708404d15aba3eb28b24c39", "guid": "bfdfe7dc352907fc980b868725387e9800260b0ff6bd074c8bdbb7a8c7ac0674"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7c6ba2478b3210fe6df74740bb8d3d4", "guid": "bfdfe7dc352907fc980b868725387e98fe3de5bd3d36a17e0d9aa10a554a91b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbd65734ea31c880dd80383876767885", "guid": "bfdfe7dc352907fc980b868725387e986d32a954ad28f8654b283264f8000ce4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae6f4a3cff8c382b418d83255460a6a4", "guid": "bfdfe7dc352907fc980b868725387e98235040965da858be0295f7b555c2e118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b3a9fd1cfd0965a0c6a9ebf2e4092e4", "guid": "bfdfe7dc352907fc980b868725387e982fbf2a8321bfaa1dd406916c3062e75e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831d220ebcfdf73907c978bc0c354b892", "guid": "bfdfe7dc352907fc980b868725387e98d2cc5d5d922868210f4715e81b020063"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a451e68c679c88703d38a11b125f3049", "guid": "bfdfe7dc352907fc980b868725387e9817d42fd3684b39a67bed167a984be3b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a90c325c7c2f95890e15e4c676dcc1bc", "guid": "bfdfe7dc352907fc980b868725387e982186516c38e2b047b8d85b9e3abec8f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c91d55294af9da618bac2c4ec1556ca", "guid": "bfdfe7dc352907fc980b868725387e983ba67aae03bec559656bb0fcb9a543ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7f6221ca16ad923053734f954245215", "guid": "bfdfe7dc352907fc980b868725387e981f15a3129063fc818f963bbd86131b10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f01c948177267aa860b098a54508181", "guid": "bfdfe7dc352907fc980b868725387e980c04b925abe5a7dbcd2bce08777fa56a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d04b3f0f0ff43fa69ea0bdfe9a91a947", "guid": "bfdfe7dc352907fc980b868725387e988a8775245f5f0001bf1ff488c004e6c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890c23a190a1cbfb39817212fb925e66d", "guid": "bfdfe7dc352907fc980b868725387e98b980576b5b2e5f46ce92bf1270d8250c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b984756694e5cdb580f64345a54fb679", "guid": "bfdfe7dc352907fc980b868725387e982d2886a197ccb6c2dac6a56d578acaa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc0b16ab56c9e64033e4c13c55756cb7", "guid": "bfdfe7dc352907fc980b868725387e984e73d88ae412b41a6f15245d2d5c16ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe5af995963733569ff610b6e263abd1", "guid": "bfdfe7dc352907fc980b868725387e984eeeaa430cd3c651fec8c5bd40d762e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98240989f4c1d20afa947fac77fee32ef1", "guid": "bfdfe7dc352907fc980b868725387e984921e40e36d4c16aea97616d7b65ec84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ee6c09d2f141ab9628a10e63e753a7", "guid": "bfdfe7dc352907fc980b868725387e9802fa17a9388f990ed77caff2a3799c8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98671c3959743dbf1a0d03100b7f7b7390", "guid": "bfdfe7dc352907fc980b868725387e98c09a7ffa31156a82f74af06abc9ab20a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac370810a78efd59920b3802dd1a2d51", "guid": "bfdfe7dc352907fc980b868725387e986531517c4dbf0bee7d8fc09647df214f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842ab9ff1fd359b170c5c9f48dfea3bf3", "guid": "bfdfe7dc352907fc980b868725387e98b56566c8310d63c0e874e3feda6f60cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a41d0adf1969fb176a5081997ef3a4f", "guid": "bfdfe7dc352907fc980b868725387e98fea6e8fdefc722fe9995117b432c1180"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc0e3a838f8f91c463fa2efb9693484d", "guid": "bfdfe7dc352907fc980b868725387e9800eb72f48284f19e8aeb3435bd73353e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5cd16751fe0710e27c511d182092135", "guid": "bfdfe7dc352907fc980b868725387e9879e53455071d94a93e77b6a6769ba4ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987753fe95d4125dadc00bd0ae308e20c1", "guid": "bfdfe7dc352907fc980b868725387e982c0e37396d42e8820d4f94ae1b478e55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f2530c40cca85f6c7550e27d13586ae", "guid": "bfdfe7dc352907fc980b868725387e9819b5b42036bb6397bb0db36c68e51c06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858ec530886ff6448a2ffeabb690bdbfb", "guid": "bfdfe7dc352907fc980b868725387e98e5ea4a3398e3e2c8541039f9b5b7ccf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98194ca4b4f981d2c1f290790f2c5a8b68", "guid": "bfdfe7dc352907fc980b868725387e986a94a9d362f4b91d447246f6dd18200a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842630577797e5ab55076ae13c054777a", "guid": "bfdfe7dc352907fc980b868725387e98399c41c7701fbf6e0e4b68cf7346d6fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3ac25ba0c7018069c8dbee14af10b47", "guid": "bfdfe7dc352907fc980b868725387e983550e714aa8401daab485d95b3476a24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874e47da39ef0a2e351d22893dbc03d51", "guid": "bfdfe7dc352907fc980b868725387e98c68be0c0d0ac92ea76aae4429e97ee89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdbf78f79809aae6db9d4cf15d63c5a9", "guid": "bfdfe7dc352907fc980b868725387e98ffcb057fbbb7653d76250d4829f8398f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3f77b69d5b1a179d7148bfb3eec13b9", "guid": "bfdfe7dc352907fc980b868725387e9870a2ed7e56708a76e685aa58afdf1ccd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885afbe4f6f9d7b8e7fb66b77569f84f1", "guid": "bfdfe7dc352907fc980b868725387e989d8d8fa1f80f856f4d19e1d444f01fe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806c6afa7ee96c0bf8e9fef540d7f24bc", "guid": "bfdfe7dc352907fc980b868725387e988e3cdc0b82521f3db176f80d8a58cf55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819602cdc92e80b2d736457bcd5e34c6a", "guid": "bfdfe7dc352907fc980b868725387e98a8cdf1d27bc6d8ac8204f41d35e953bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dba1595fda2244847f384e38c70e2173", "guid": "bfdfe7dc352907fc980b868725387e983fc914a8448b684bcd62fad4f10fc0e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989120b3a07f7b9486b79caaebee726872", "guid": "bfdfe7dc352907fc980b868725387e989238987c6d4dc35d3e5a322f03007bd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e126549aed43eaf2156c97c0a99aaeb1", "guid": "bfdfe7dc352907fc980b868725387e982eab10a4ae07b13a5937761a89cfc75b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989970a9f54256bd69e78372e73c9459c6", "guid": "bfdfe7dc352907fc980b868725387e98f8571cdf6d6b84a85351970ad65fff0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee14fba339c681aeae9e7d47042c4119", "guid": "bfdfe7dc352907fc980b868725387e9881892e040425214db3fb2578de4ee2d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866b06c740e54567ad277f777624fed3b", "guid": "bfdfe7dc352907fc980b868725387e98aacee0605a3cd4696ff801429ed87b22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1bb715b30bc295db2cfae764ea79c9a", "guid": "bfdfe7dc352907fc980b868725387e98d9b5dde53a97a88c61a30832bd559cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c91563fe19eab483e03d513788cd8c61", "guid": "bfdfe7dc352907fc980b868725387e98a1aa824512ffbe0aa8647091959b911c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98354db8e6f462e7eea2103185e8f52ac6", "guid": "bfdfe7dc352907fc980b868725387e98b188cb742539656a82a35a3f16981bcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d94694c83105e147d376043ad05b87fe", "guid": "bfdfe7dc352907fc980b868725387e982c3450d903e9d96a5538e5a09aaa07b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825c60cf1d07fd9b662f5811857386672", "guid": "bfdfe7dc352907fc980b868725387e981f9782dc8dd741f99ab0e81bb8860697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98925bec583bc031817092d2fe5101071d", "guid": "bfdfe7dc352907fc980b868725387e984025d19e45142061f02f60c129fc05fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b51daa83b1be8a5f9c2ee8c88005443", "guid": "bfdfe7dc352907fc980b868725387e98a01b381e328902ff95f15bfd07e38f5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986abd5ce61ee9894fdb3164f5bd2c549d", "guid": "bfdfe7dc352907fc980b868725387e988a30d3d8acd4e6d20602b67da47193be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bef294a4309d856e4ff8f3f0bc7d7e2", "guid": "bfdfe7dc352907fc980b868725387e981a0cc038ae1b1fd25719adda27976cce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829b624841fc1d1b75b14654ccf0ce200", "guid": "bfdfe7dc352907fc980b868725387e9874905207c3e9df630c4cc2cc4b8f326a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb5b687f1f834ded8c16e3872770bfc0", "guid": "bfdfe7dc352907fc980b868725387e98dbff2b3ddff5858342d98184e5628d72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985674208bd38e55a6a35e907e5e7f017a", "guid": "bfdfe7dc352907fc980b868725387e9815758cc3f525d96d70df78b67a43d851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98064e0fade0e33f0c08aed1c4dd5d9158", "guid": "bfdfe7dc352907fc980b868725387e9804e4f56dc3167681cf691e8596c71146"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ad95c2d1752200edcf1e1f91cc7497c", "guid": "bfdfe7dc352907fc980b868725387e9853ccc12ddfac15e8ffa7df5827562029"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db332473b109e3e97d2d0cfc02dd5662", "guid": "bfdfe7dc352907fc980b868725387e986f0a1052aa0ca0cf8f1905181b39665f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccd10531e43e0306fab72e69ca60387e", "guid": "bfdfe7dc352907fc980b868725387e98f9ac30e6751ea202187da901159227a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984699f28f05f4a6b6d12a9cdbf524c964", "guid": "bfdfe7dc352907fc980b868725387e98223c0539cd38711d7a444ccf4ac8bb58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfc4896f557af4c3162507fe8166d39f", "guid": "bfdfe7dc352907fc980b868725387e98459f9b8d84f37936b5a3961f1212486a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98820cbf6777041517d7f625406fb24a74", "guid": "bfdfe7dc352907fc980b868725387e98163c620f28a83f56ae09907b19a50459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a9dd902ccc9faba6005a5fb2eed0cf5", "guid": "bfdfe7dc352907fc980b868725387e98fac7be43458c86a240c9c227d1209259"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98781a80d9a5e1c908708e7d3c8255c01a", "guid": "bfdfe7dc352907fc980b868725387e98b8750ef6b70c38f5d3efed43794b1885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0b29b958fc0af0d39127cec98a06a45", "guid": "bfdfe7dc352907fc980b868725387e98f656c306a73ee677f40cdfa9f9ca4557"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986837435669ed88d359c7f2533532178c", "guid": "bfdfe7dc352907fc980b868725387e9821526f14d99f3d770dc12a04027008fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bae0872cba26afda6b6a3432926838a4", "guid": "bfdfe7dc352907fc980b868725387e987b6b74deff72959662529d87a81ecb8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b73996502646a1aab3d90bb5e2a56d3d", "guid": "bfdfe7dc352907fc980b868725387e984ff2b8aa6691cf2cf57960c7512f48a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98178a0b688c9b4ad4afb4234220d05b23", "guid": "bfdfe7dc352907fc980b868725387e9837579c29f04e4c3539ec94bc445472f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98788439430388da8d8a30b3c6a526b089", "guid": "bfdfe7dc352907fc980b868725387e986945e5c3e4e41047a8980cb599c88d0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a98970be7c640c3d3d01a6e715ff2a61", "guid": "bfdfe7dc352907fc980b868725387e983bd0acd644bb0ce8df00d1c587ead8ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b81629ad351e6fb22d3b9db3909f1d0", "guid": "bfdfe7dc352907fc980b868725387e98d946d86b98f2497902130a0062ebb0a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831ab99b4852edeb7ea35489cfbd9b774", "guid": "bfdfe7dc352907fc980b868725387e98d55d34d789a1f86ac8f3ad9acbb944b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e06e9a8a272fa81f6bdb4a0579e9652", "guid": "bfdfe7dc352907fc980b868725387e98981c16ccd4d794cf02683c13599f7221"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc4634deb313dfb1800412cd278ba7f8", "guid": "bfdfe7dc352907fc980b868725387e98ddc8ba56e16a719422a7dc504cf71724"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbed926b4f31f4f6308e675d61e3744f", "guid": "bfdfe7dc352907fc980b868725387e98a25d3bfff5529b1dd5969eb1a9425c5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aea1d29f52b76a5e6f508ba3606d76c5", "guid": "bfdfe7dc352907fc980b868725387e98456eb65b214512cac8c9321656ce3a72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874503e61339fef1b84245e6f57e7c0e5", "guid": "bfdfe7dc352907fc980b868725387e982fae223ba487719295cf409bf39b44e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ddb0f0a35fd87a1529804ecd863d2d3", "guid": "bfdfe7dc352907fc980b868725387e98b6fd83cd4dac6dd0f302fc9fddc5720b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981587989eeaeebd5f3d769e81aeda8314", "guid": "bfdfe7dc352907fc980b868725387e98c5276f2250204d3bcbac5138a0874abf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bf8f7fd00796f3174d94c9724179db2", "guid": "bfdfe7dc352907fc980b868725387e9895aecedfcb719baaead44cbb1729c5e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98541648502f98b5fdf739e54b179b3730", "guid": "bfdfe7dc352907fc980b868725387e98a24fd2b423e05dd6d9186fd39827ab84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e9a8965714057fdb664cd4e2ad0ed4d", "guid": "bfdfe7dc352907fc980b868725387e98f02306045a58d555bff9be2e61d79d11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c7b6fcc2b36c64981a43e4f9926b8de", "guid": "bfdfe7dc352907fc980b868725387e98467fa1467989c14c4013c3a50fa45e13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf09a7d1d285e2eb8d6a22fbceec6af8", "guid": "bfdfe7dc352907fc980b868725387e985db12a2072ed67e7cf46c4787669da4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b413e83d3fcf4abae2df140ea1e4107e", "guid": "bfdfe7dc352907fc980b868725387e9872c44737bd90b83b1c578d0a29f262cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cbd62f60cc52606d8465c4dffd8a31c", "guid": "bfdfe7dc352907fc980b868725387e9883ab9baced37a0d9c4ff931dc6447f3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b53c10a8ecb248a2703ac7e1910ded86", "guid": "bfdfe7dc352907fc980b868725387e986dec1fa323eb105bd353a2a879ca4d07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c208000c4d3e831ec171466bbfc74124", "guid": "bfdfe7dc352907fc980b868725387e98fd2bf27ca6ef5155fea67bbae44572cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dd221cf207a19ea287d5a256c6b3998", "guid": "bfdfe7dc352907fc980b868725387e9827160be7aaceae07b878cfad218603e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985447fb3002afc9e701381a51864d3e28", "guid": "bfdfe7dc352907fc980b868725387e98285146f588a09e3f0c8cc0afcd61543c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d326977de0fcbad9f2f5f8d9bbbed6d", "guid": "bfdfe7dc352907fc980b868725387e980e247b30b26c89bee42e7d6f81d0f3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98940e4e66c2fb4cfd755e166d74b8892a", "guid": "bfdfe7dc352907fc980b868725387e98ea3ee11b2da1ad3ceaaf3433256ca91a"}], "guid": "bfdfe7dc352907fc980b868725387e98cac5758aaab041b306b3055c248885f4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e98027d34dd1e1770c79dd54f5404987fd6"}], "guid": "bfdfe7dc352907fc980b868725387e98d8f70b5ff677ed02b77232961e037957", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b7d49ad1fe66522e6eab9248dd2331d6", "targetReference": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de7d0960135bbd3ac12243d305280ba9", "guid": "bfdfe7dc352907fc980b868725387e986d52c275603be68942df62606ca0d8e3"}], "guid": "bfdfe7dc352907fc980b868725387e98ba929dd1b11c60f9a4f8fd06e4eaa3e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e985f0ec3a68eeed5241cb87afb05bcc380", "name": "OrderedSet"}, {"guid": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b", "name": "flutter_inappwebview_ios-flutter_inappwebview_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98a562549a031aeda8bf3440b79b3420bc", "name": "flutter_inappwebview_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9810acd6d3a97e7ef91b90dda5618dc5c0", "name": "flutter_inappwebview_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}