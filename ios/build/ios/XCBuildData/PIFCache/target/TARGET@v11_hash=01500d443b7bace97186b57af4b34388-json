{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce0a8fac8e14bcfdce9401eaca55e7de", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b656b11b47d382d912b509e413431ce5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c8f917aec9e091fa99c44336abf4a941", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986c78c26ba49c4cecd218b553b7402525", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c8f917aec9e091fa99c44336abf4a941", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_STORAGE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e70cc4693da9e072d2266841caaef83", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985db9e20853922e57336276ad6c4e95cd", "guid": "bfdfe7dc352907fc980b868725387e98ee3140cab907ed7bbedadecacdd9ff65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860b4886a56174757fe43a2bfe7363756", "guid": "bfdfe7dc352907fc980b868725387e98f90c200d6f782392a9fe72388926ab0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840a449f7c59f7da09b9a119a2e46a455", "guid": "bfdfe7dc352907fc980b868725387e98fbf281565cfa7108ae0667ebb14758e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b74b8be6d760e3750a5402b998cf3207", "guid": "bfdfe7dc352907fc980b868725387e9888e61db328aa9182ba474909a35c5266", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c2b43c7d28f2ade8905e475f34fb768", "guid": "bfdfe7dc352907fc980b868725387e98596b1ca7c2c953fa663eda0e6463b470", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b5143a53498753286b5949ea37dfa13", "guid": "bfdfe7dc352907fc980b868725387e988e54738023e807c8d482a4f7475c7b8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af13337e94524e09020d8292825a248a", "guid": "bfdfe7dc352907fc980b868725387e9864e5358b2b143fa1aca2f824afcbf234", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb415b013ad900af532ffc4371ef70be", "guid": "bfdfe7dc352907fc980b868725387e98298391a7091d132ae77149b5a828410b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e8a992c96be8f3ff1a5787813f9f107", "guid": "bfdfe7dc352907fc980b868725387e982782ef7e5d27681f33cea38f531f47c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fd703dbd378b7c3b85b147de99b7a90", "guid": "bfdfe7dc352907fc980b868725387e98de621f3023f58c6dda61f9b2a426de8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5e979bc7ee0d32340d63a23a2e0d832", "guid": "bfdfe7dc352907fc980b868725387e98298a19ea4b2f6bc9ec0b69aaa6be9a93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859e354a4dd794c94b5f6376b149f5df9", "guid": "bfdfe7dc352907fc980b868725387e98fa89e0b4307dce7aa1393eae4930440c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988318fac9a4b7cda1bb951f5bc08616af", "guid": "bfdfe7dc352907fc980b868725387e986a650bca4e13200ab03128018b837957", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98636ce5ec741064a36edc732aadf3a532", "guid": "bfdfe7dc352907fc980b868725387e985dc90b35a907c37574178db73110ef31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee07e5d1bd4218f7049a30a67ac5001", "guid": "bfdfe7dc352907fc980b868725387e983f3d54b277f61a330c144623b13d3adb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed26e7f7cc1d232772e3728b9da4fb13", "guid": "bfdfe7dc352907fc980b868725387e981e208dd971d7a7a5c4e74adf0688cebc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b4d92045e359aea6fc666f790ce5d7c", "guid": "bfdfe7dc352907fc980b868725387e98dfafcf2bee5b471383517031133e8141", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f8e100ce079dc80caf84c9d1ebf364c", "guid": "bfdfe7dc352907fc980b868725387e98e61f199372ee6da3ffdcc1b778b4c9b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cbf218248e637d78f1c33d9bb249782", "guid": "bfdfe7dc352907fc980b868725387e980d6d7ad57ae980cb1c4554886665dd13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822f051f4b7c94e52d766de7d7a173c55", "guid": "bfdfe7dc352907fc980b868725387e98c3c8ac11eb4412385ea0aea901358090", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1055139a88df78acef0e61b5e96decf", "guid": "bfdfe7dc352907fc980b868725387e98fd1db65be2be80507990cfe0b3e99efc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edd3726b8b743ee4ee00a867ab6f4b09", "guid": "bfdfe7dc352907fc980b868725387e984e1bde28f5d9be06d3e44f3bf84ae0dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f594f108e72610b9cdcdee0d357bea5d", "guid": "bfdfe7dc352907fc980b868725387e98b3594a9b230a17959b187bd00b6914c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea4f821f50c10689429de7d18cb0e170", "guid": "bfdfe7dc352907fc980b868725387e983f58131e9dee5976af24353bf7c99c29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aac3cfa919f033ec5142f1f61800e190", "guid": "bfdfe7dc352907fc980b868725387e98c564d02f1d07e8c5b1378e898fee15b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883093fd98409634b8cdd1d6a10ff3734", "guid": "bfdfe7dc352907fc980b868725387e9851d51fcb7f1217b0e91cb0945325e0e9"}], "guid": "bfdfe7dc352907fc980b868725387e986004d9caeb35b4abad195321094e0399", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987abd48e82427b950d54db86d322aaa7b", "guid": "bfdfe7dc352907fc980b868725387e988ebdab30be476343f3faf0450e1044a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a075e5803989942309ad397bab24512", "guid": "bfdfe7dc352907fc980b868725387e980f2822b5795bdb519c5c526987ab348a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba040f5c376154eb516d1e9c5645dca4", "guid": "bfdfe7dc352907fc980b868725387e985d2b9b1132b9606bce9f9f56e611b07c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a5bc76ceae8e3ed4b56439e0fb0ca6a", "guid": "bfdfe7dc352907fc980b868725387e986b13a4e15e02b7a81d8f48cede047453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808f6646dbde9aa6251f22cbe4d86f09a", "guid": "bfdfe7dc352907fc980b868725387e98bf9235cf5b7d11a4442f4f77febc9fb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98485c9eb0bb1bba5b40f198c28ecf53de", "guid": "bfdfe7dc352907fc980b868725387e98d1f5b8652af6c714d0b5ca09693e183b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e36e5da551036916a831a27972fda6a", "guid": "bfdfe7dc352907fc980b868725387e9822e2d8c2d5faee9069b8154e9c329ff3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ce43f13bc2212d1cec26e5bc7bc671", "guid": "bfdfe7dc352907fc980b868725387e9819ab11c3c26816f0481ee2d84d670f77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989008ac10d7c07e519bd7a39dbe6ff2d0", "guid": "bfdfe7dc352907fc980b868725387e9888ea0d218e3be149ec8741a0096ab92d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983df9724b91b8ec75ec8482da875e3205", "guid": "bfdfe7dc352907fc980b868725387e987625a31579b01e1ce9e91980fd35057b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f740b6c4a20d357234e49fb9a077fc3", "guid": "bfdfe7dc352907fc980b868725387e986d1919bfcd425d00a0bd0e86cf62a556"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98705bd2ec1f0df4830ffd0cefbb5bd467", "guid": "bfdfe7dc352907fc980b868725387e98c0cf4bed312ea106f1806022fc433a7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d26729399bbc671578deeaec839b2594", "guid": "bfdfe7dc352907fc980b868725387e980623233e1069bd2ee78fca55c5ee8414"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1e4267601fe7b21ab75e17669adf7f4", "guid": "bfdfe7dc352907fc980b868725387e9860708091a926a3eb6d087bb0c1e43438"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5a711668fbb15ffd43f7e2bec3a9670", "guid": "bfdfe7dc352907fc980b868725387e98086a1f281abaa5ebbe79f5112796253b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7f930ad3b0c2d0182e6bc80aca53625", "guid": "bfdfe7dc352907fc980b868725387e987dec91952cd2734c5bf2d13506af8a6f"}], "guid": "bfdfe7dc352907fc980b868725387e9880c76444e6be9868b461b1fe4217eacb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9395b190a68bd693504d849861b9aa0", "guid": "bfdfe7dc352907fc980b868725387e9893abd1c5652908b843c8bdbe37ca606c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c81c87c9ae76013da3d981243a0bf06", "guid": "bfdfe7dc352907fc980b868725387e98c4e9e62d0fda1eb0468c1096722c32fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b02334b49441ad2a302268dd5ac4cf25", "guid": "bfdfe7dc352907fc980b868725387e9824a04a0860e8267e907b21c0fce61a4f"}], "guid": "bfdfe7dc352907fc980b868725387e98f17f3cc5d908f945a8297525e79c1a09", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9833a0e8991bd854f125574bee92c52791", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9833ce236bb86cb4e8454c0aeb3ef3b6a5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}